import { Button } from "@repo/ui/components/button";
import { MenuIcon } from "lucide-react";

const ToggleSidebar = (props: any) => {
  const { margin, sidebar, setSidebar } = props;
  return !margin ? (
    <Button
      variant="link"
      className="pt-0 mr-2 fs-20"
      onClick={() => {
        setSidebar(!sidebar);
      }}
    >
      {/* <Icon icon={"far fa-bars"} variant="dark" /> */}
      <MenuIcon />
    </Button>
  ) : null;
};

export default ToggleSidebar;
