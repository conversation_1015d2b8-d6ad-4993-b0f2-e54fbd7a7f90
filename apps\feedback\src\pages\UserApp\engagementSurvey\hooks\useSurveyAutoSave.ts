import { useState, useCallback } from 'react';
import {
  saveQuestionResponse,
  saveCommentResponse,
  deleteQuestionResponse,
  saveDemographicsResponse,
  deleteDemographicsResponse,
  type EngagementSurveyData
} from '@/services/engagementSurveyService';

export const useSurveyAutoSave = (
  surveyData: EngagementSurveyData | null,
  setSurveyData: (data: EngagementSurveyData | null) => void
) => {
  const [isSaving, setIsSaving] = useState(false);
  const [saveTimeout, setSaveTimeout] = useState<ReturnType<typeof setTimeout> | null>(null);
  const [lastSaved, setLastSaved] = useState<Date>(new Date());

  // Auto-save functionality with debouncing
  const debouncedSave = useCallback((saveFunction: () => Promise<void>) => {
    if (saveTimeout) {
      clearTimeout(saveTimeout);
    }

    setIsSaving(true);

    const timeout = setTimeout(async () => {
      try {
        await saveFunction();
        setLastSaved(new Date());
      } catch (err) {
        console.error('Error saving response:', err);
      } finally {
        setIsSaving(false);
      }
    }, 1000); // 1 second debounce

    setSaveTimeout(timeout);
  }, [saveTimeout]);

  const handleResponseUpdate = useCallback((
    responseId: string,
    questionId: string,
    value: any,
    existingResponseId?: string,
    shouldDelete?: boolean,
    onComplete?: () => void,
    onNewResponseId?: (newId: string) => void
  ) => {
    // Optimistic update - update local state immediately
    if (surveyData) {
      const updatedSurveyData = { ...surveyData };

      // Update the question response in all sections
      updatedSurveyData.sections = updatedSurveyData.sections.map(section => ({
        ...section,
        questions: section.questions.map(question => {
          if (question.id === questionId) {
            if (shouldDelete) {
              // Remove the response when deleting - clear the response completely
              return {
                ...question,
                response: undefined
              };
            } else {
              return {
                ...question,
                response: {
                  id: question.response?.id || existingResponseId || '',
                  value: value,
                  is_valid: true
                }
              };
            }
          }
          return question;
        })
      }));

      // Update questions array as well
      updatedSurveyData.questions = updatedSurveyData.questions.map(question => {
        if (question.id === questionId) {
          if (shouldDelete) {
            // Remove the response when deleting - clear the response completely
            return {
              ...question,
              response: undefined
            };
          } else {
            return {
              ...question,
              response: {
                id: question.response?.id || existingResponseId || '',
                value: value,
                is_valid: true
              }
            };
          }
        }
        return question;
      });

      // Update completion percentage optimistically
      const totalQuestions = updatedSurveyData.questions.length;
      const answeredQuestions = updatedSurveyData.questions.filter(q => {
        const value = q.response?.value;
        return value !== undefined && value !== null && value !== '';
      }).length;
      updatedSurveyData.completion = totalQuestions > 0 ? Math.round((answeredQuestions / totalQuestions) * 100) : 0;

      setSurveyData(updatedSurveyData);
    }

    debouncedSave(async () => {
      // Check if this question is from a demographics section
      const question = surveyData?.questions.find(q => q.id === questionId);
      const isDemographicsQuestion = surveyData?.sections.find(s => s.id === question?.section)?.isDemographic;

      if (shouldDelete && existingResponseId) {
        // Delete the existing response
        console.log('🗑️ API DELETE - Deleting question response:', {
          questionId,
          existingResponseId,
          isDemographicsQuestion,
          verb: 'DELETE'
        });

        if (isDemographicsQuestion) {
          await deleteDemographicsResponse(existingResponseId);
        } else {
          await deleteQuestionResponse(existingResponseId);
        }
      } else {
        // Save or update the response
        const verb = existingResponseId ? 'PATCH' : 'POST';
        console.log(`${verb === 'POST' ? '📝' : '✏️'} API ${verb} - Saving question response:`, {
          questionId,
          value,
          existingResponseId,
          isDemographicsQuestion,
          verb
        });

        const apiResponse = isDemographicsQuestion
          ? await saveDemographicsResponse(responseId, questionId, value, existingResponseId)
          : await saveQuestionResponse(responseId, questionId, value, existingResponseId);

        // If this was a POST request, update the local state with the new response ID
        if (!existingResponseId && apiResponse?.data?.id && surveyData) {
          const newResponseId = apiResponse.data.id;
          console.log('📝 POST response received, updating local state with new ID:', newResponseId);

          // Call the callback to update QuestionRenderer's currentResponseId immediately
          if (onNewResponseId) {
            onNewResponseId(newResponseId);
          }

          const updatedSurveyData = { ...surveyData };
          // Update sections
          updatedSurveyData.sections = updatedSurveyData.sections.map(section => ({
            ...section,
            questions: section.questions.map(question => {
              if (question.id === questionId) {
                return {
                  ...question,
                  response: {
                    id: newResponseId,
                    value: value,
                    is_valid: true
                  }
                };
              }
              return question;
            })
          }));
          // Update questions array
          updatedSurveyData.questions = updatedSurveyData.questions.map(question => {
            if (question.id === questionId) {
              return {
                ...question,
                response: {
                  id: newResponseId,
                  value: value,
                  is_valid: true
                }
              };
            }
            return question;
          });

          // Recalculate completion percentage after API response
          const totalQuestions = updatedSurveyData.questions.length;
          const answeredQuestions = updatedSurveyData.questions.filter(q => {
            const value = q.response?.value;
            return value !== undefined && value !== null && value !== '';
          }).length;
          updatedSurveyData.completion = totalQuestions > 0 ? Math.round((answeredQuestions / totalQuestions) * 100) : 0;

          setSurveyData(updatedSurveyData);
        }
      }
      if (onComplete) {
        onComplete();
      }
    });
  }, [debouncedSave, surveyData, setSurveyData]);

  const handleCommentUpdate = useCallback((
    responseId: string,
    questionId: string,
    comment: string,
    commentId?: string,
    onNewResponseId?: (newId: string) => void
  ) => {
    // Update local state optimistically for all questions that collect feedback
    if (surveyData) {
      const question = surveyData.questions.find(q => q.id === questionId);
      if (question) {
        const updatedSurveyData = { ...surveyData };

        if (question.resourcetype === 'QuestionInput' || question.type === 'QuestionInput') {
          // For QuestionInput types, update the response field
          updatedSurveyData.sections = updatedSurveyData.sections.map(section => ({
            ...section,
            questions: section.questions.map(q => {
              if (q.id === questionId) {
                return {
                  ...q,
                  response: {
                    id: commentId || q.response?.id || '',
                    value: comment,
                    is_valid: true
                  }
                };
              }
              return q;
            })
          }));
        } else {
          // For regular questions with collect_feedback, update the feedback field
          updatedSurveyData.sections = updatedSurveyData.sections.map(section => ({
            ...section,
            questions: section.questions.map(q => {
              if (q.id === questionId) {
                return {
                  ...q,
                  feedback: comment,
                  commentId: commentId || q.commentId
                };
              }
              return q;
            })
          }));
        }

        // Update questions array as well
        if (question.resourcetype === 'QuestionInput' || question.type === 'QuestionInput') {
          // For QuestionInput types, update the response field
          updatedSurveyData.questions = updatedSurveyData.questions.map(q => {
            if (q.id === questionId) {
              return {
                ...q,
                response: {
                  id: commentId || q.response?.id || '',
                  value: comment,
                  is_valid: true
                }
              };
            }
            return q;
          });
        } else {
          // For regular questions with collect_feedback, update the feedback field
          updatedSurveyData.questions = updatedSurveyData.questions.map(q => {
            if (q.id === questionId) {
              return {
                ...q,
                feedback: comment,
                commentId: commentId || q.commentId
              };
            }
            return q;
          });
        }

        // Recalculate completion percentage
        const totalQuestions = updatedSurveyData.questions.length;
        const answeredQuestions = updatedSurveyData.questions.filter(q => {
          const value = q.response?.value;
          return value !== undefined && value !== null && value !== '';
        }).length;
        updatedSurveyData.completion = totalQuestions > 0 ? Math.round((answeredQuestions / totalQuestions) * 100) : 0;

        setSurveyData(updatedSurveyData);
      }
    }

    debouncedSave(async () => {
      const apiResponse = await saveCommentResponse(responseId, questionId, comment, commentId);

      // Update the comment ID if it's a new comment
      if (surveyData && !commentId && apiResponse?.data?.id) {
        const question = surveyData.questions.find(q => q.id === questionId);
        if (question) {
          const newCommentId = apiResponse.data.id;

          // Call the callback to update QuestionRenderer's currentCommentId immediately
          if (onNewResponseId) {
            onNewResponseId(newCommentId);
          }

          const updatedSurveyData = { ...surveyData };

          if (question.resourcetype === 'QuestionInput' || question.type === 'QuestionInput') {
            // For QuestionInput types, update the response field
            updatedSurveyData.sections = updatedSurveyData.sections.map(section => ({
              ...section,
              questions: section.questions.map(q => {
                if (q.id === questionId) {
                  return {
                    ...q,
                    response: {
                      id: newCommentId,
                      value: comment,
                      is_valid: true
                    }
                  };
                }
                return q;
              })
            }));
          } else {
            // For regular questions with collect_feedback, update the commentId field
            updatedSurveyData.sections = updatedSurveyData.sections.map(section => ({
              ...section,
              questions: section.questions.map(q => {
                if (q.id === questionId) {
                  return {
                    ...q,
                    feedback: comment,
                    commentId: newCommentId
                  };
                }
                return q;
              })
            }));
          }
          // Update questions array
          if (question.resourcetype === 'QuestionInput' || question.type === 'QuestionInput') {
            // For QuestionInput types, update the response field
            updatedSurveyData.questions = updatedSurveyData.questions.map(q => {
              if (q.id === questionId) {
                return {
                  ...q,
                  response: {
                    id: newCommentId,
                    value: comment,
                    is_valid: true
                  }
                };
              }
              return q;
            });
          } else {
            // For regular questions with collect_feedback, update the commentId field
            updatedSurveyData.questions = updatedSurveyData.questions.map(q => {
              if (q.id === questionId) {
                return {
                  ...q,
                  feedback: comment,
                  commentId: newCommentId
                };
              }
              return q;
            });
          }

          // Recalculate completion percentage after API response
          const totalQuestions = updatedSurveyData.questions.length;
          const answeredQuestions = updatedSurveyData.questions.filter(q => {
            const value = q.response?.value;
            return value !== undefined && value !== null && value !== '';
          }).length;
          updatedSurveyData.completion = totalQuestions > 0 ? Math.round((answeredQuestions / totalQuestions) * 100) : 0;

          setSurveyData(updatedSurveyData);
        }
      }
    });
  }, [debouncedSave, surveyData, setSurveyData]);

  return {
    isSaving,
    lastSaved,
    handleResponseUpdate,
    handleCommentUpdate
  };
};
