import React from 'react';
import { type EngagementSurveyData, type SurveySection } from '@/services/engagementSurveyService';
import { useNavigate } from 'react-router';
import { Button } from '@repo/ui/components/button';
import { USER_ROUTES } from '@/app.routes';

interface SurveySidebarProps {
  isLoading: boolean;
  surveyData: EngagementSurveyData | null;
  currentSection: SurveySection | null;
  onSectionSelect: (section: SurveySection) => void;
}

const SurveySidebar: React.FC<SurveySidebarProps> = ({
  isLoading,
  surveyData,
  currentSection,
  onSectionSelect
}) => {
  const navigate = useNavigate();
  return (
    <div className="w-[300px] bg-gray-100 dark:bg-gray-800 h-full p-4 border-r dark:border-gray-700 flex-shrink-0 overflow-y-auto">
      <Button
        variant="ghost"
        onClick={() => navigate(USER_ROUTES().dashboard.surveyList)}
        className="text-blue-600 dark:text-blue-400 hover:bg-blue-600 p-1"
      >
        ← Back
      </Button>
      <hr className='my-4 dark:border-gray-600' />
      {/* Survey End Date */}
      <div className="mb-6">
        <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Survey Ends On</h3>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          {isLoading ? (
            <div className="h-4 w-32 bg-gray-300 dark:bg-gray-600 rounded animate-pulse"></div>
          ) : (
            surveyData?.meta.endDate ?
              new Date(surveyData.meta.endDate).toLocaleDateString('en-US', {
                month: 'short',
                day: 'numeric',
                year: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
              }) : 'Not set'
          )}
        </p>
      </div>
      <hr className='my-4 dark:border-gray-600' />
      {/* Survey Status */}
      <div className="mb-6">
        <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Survey Status</h3>
        <div className="space-y-1">
          <div className="flex justify-between text-sm">
            <span className="text-gray-600 dark:text-gray-400">{surveyData?.questions.length || 0}</span>
            <span className="text-gray-500 dark:text-gray-400">Questions</span>
          </div>
          <div className="flex justify-between text-sm">
            <span className="text-blue-600 dark:text-blue-400 font-medium">
              {surveyData?.questions.filter(q => {
                const value = q.response?.value;
                return value !== undefined && value !== null && value !== '';
              }).length || 0}
            </span>
            <span className="text-gray-500 dark:text-gray-400">Completed</span>
          </div>
        </div>
      </div>
      <hr className='my-4 dark:border-gray-600' />
      {/* Categories */}
      <div>
        <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Categories</h3>
        <div className="space-y-1">
          {isLoading ? (
            Array(3).fill(0).map((_, index) => (
              <div key={index} className="h-6 w-full bg-gray-300 dark:bg-gray-600 rounded animate-pulse mb-1"></div>
            ))
          ) : (
            surveyData?.sections.map((section) => {
              const sectionQuestions = section.questions;
              const completedQuestions = sectionQuestions.filter(q => {
                const value = q.response?.value;
                return value !== undefined && value !== null && value !== '';
              });
              const questionsLeft = sectionQuestions.length - completedQuestions.length;

              return (
                <div
                  key={section.id}
                  className={`p-2 rounded cursor-pointer text-sm ${currentSection?.id === section.id
                    ? 'bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-300 border border-blue-200 dark:border-blue-700'
                    : 'hover:bg-gray-200 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300'
                    }`}
                  onClick={() => onSectionSelect(section)}
                >
                  <div className={`font-medium ${section.isDemographic ? 'text-purple-700 dark:text-purple-400' : ''}`}>
                    {section.title}
                  </div>
                  {questionsLeft > 0 && (
                    <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                      {questionsLeft} questions left
                    </div>
                  )}
                </div>
              );
            })
          )}
        </div>
      </div>
    </div>
  );
};

export default SurveySidebar;
