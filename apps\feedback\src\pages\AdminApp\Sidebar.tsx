// import { useEffect, useRef, useState } from "react";
import { Link, NavLink } from "react-router";
import DASHBOARD_LINKS from "./admin-app-meta";
import {
  TooltipProvider,
  Tooltip,
  TooltipTrigger,
  TooltipContent,
} from "@repo/ui/components/tooltip";
import { useNavigate } from "react-router";
import useSession from "@/unmatched/modules/session/hook";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@repo/ui/components/dropdown-menu";
import { Button } from "@repo/ui/components/button";

function Sidebar() {
  const navigate = useNavigate();
  const session = useSession();
  return (
    <TooltipProvider>
      <div className="dashboard-sidebar w-[60px] bg-secondary h-full fixed left-0 top-0">
        <div className="text-center flex flex-col h-full">
          <div className="flex-grow flex items-center flex-col gap-3 py-3 nav_menu">
            {DASHBOARD_LINKS.filter((el) => el.id !== 6).map(
              (item) => (
                // <div key={`sidebar-${item.id} un-sidebar p-3`}>
                <Tooltip key={`sidebar-${item.id}`}>
                  <TooltipTrigger>
                    <NavLink
                      to={item.route}
                      className="text-white hover:bg-[#121b2e] flex items-center justify-center h-[36px] w-[36px] rounded"
                      onClick={() => navigate(item.route)}
                    >
                      {item.icon}
                    </NavLink>
                  </TooltipTrigger>
                  <TooltipContent side="right" sideOffset={6}>
                    <p>{item.title}</p>
                  </TooltipContent>
                </Tooltip>
              )
            )}
          </div>

          <div className="px-3 pb-5">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  className="rounded-full flex items-center justify-center h-[36px] w-[36px] border bg-primary text-white hover:text-primary"
                >
                  {session.user?.firstName?.[0]}
                  {session.user?.lastName?.[0] || ""}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent side="right" sideOffset={10}>
                <DropdownMenuLabel>
                  <p className="text-lg">
                    {session.user?.firstName} {session.user?.lastName}
                  </p>
                  <p className="text-zinc-500 font-light">
                    {session.user?.email}
                  </p>
                </DropdownMenuLabel>

                <DropdownMenuSeparator />
                <Link to="/user" className="text-sm px-2 py-2 block">Switch to user</Link>
                <a className="text-sm px-2 py-2 block" href="/">Go to Lauchpad</a>
                <DropdownMenuItem>Change Password</DropdownMenuItem>
                <DropdownMenuItem variant="destructive">Logout</DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
          {/* {item.icon} */}
          {/* <SidebarMenuItem
                buttonContent={item.icon}
                title={item.title}
                items={item.children}
                route={item.route}
                //   isActive={history.location.pathname.includes(item.route)}
                //   as={Link}
                //   pathName={history.location.pathname}
              /> */}
          {/* </div> */}
          {/* <MenuFooter>
          <SidebarMenuItem
            isActive
            buttonContent={name}
            bottom
            title="Session"
            items={settingRoutes}
          />
        </MenuFooter> */}
        </div>
      </div>
    </TooltipProvider>
  );
}

// const SidebarMenuItem = (props: {
//   buttonContent: React.ReactNode;
//   title: string;
//   items?: any[];
//   route?: string;
// }) => {
//   const [tooltip, setTooltip] = useState(false);
//   const [show, setShow] = useState(false);
//   const [position, setPosition] = useState({ x: 0, y: 0 });
//   // const history = useHistory();
//   const buttonRef = useRef<HTMLDivElement>(null);

//   useEffect(() => {
//     const cb = () => {
//       setShow(false);
//     };
//     window.addEventListener("click", cb);
//     return () => window.removeEventListener("click", cb);
//   });

//   const getTooltip = () => {
//     return (
//       <Overlay
//         target={() => buttonRef.current || null}
//         show={tooltip}
//         placement={"right"}
//       >
//         {(_props) => (
//           <Tooltip id={props.title} {..._props}>
//             {props.title}
//           </Tooltip>
//         )}
//       </Overlay>
//     );
//   };

//   if (props.items && props.items.length) {
//     const getDropdownItemLabel = (item: any) => {
//       if (!item.onClick) {
//         return item.title;
//         // return (
//         //   <props.as to={item.route} className="text-white">
//         //     {item.title}
//         //   </props.as>
//         // );
//       }
//       return item.title;
//     };

//     const getDropdownMenuTemplate = () => {
//       return (
//         <DropdownMenu {...position}>
//           {props.items.map((item: any) =>
//             item.type && item.type === "label" ? (
//               <Text.P1
//                 className="text-muted px-4 py-1"
//                 key={item.id}
//                 style={{
//                   borderLeftWidth: 5,
//                   borderStyle: "solid",
//                   borderColor: "transparent",
//                 }}
//               >
//                 {item.title}
//               </Text.P1>
//             ) : (
//               <props.as to={item.route} className="text-white">
//                 <DropdownItem
//                   onClick={() => (item.onClick ? item.onClick() : "")}
//                   key={item.id}
//                   as={div}
//                   className="pl-4 cursor-pointer fs-12"
//                   isActive={props.pathName.includes(item.route)}
//                 >
//                   {getDropdownItemLabel(item)}
//                 </DropdownItem>
//               </props.as>
//             )
//           )}
//         </DropdownMenu>
//       );
//     };

//     const showMenu = (evt: any) => {
//       evt.preventDefault();
//       evt.stopPropagation();
//       setShow(true);
//       const { left, top, width } = evt.currentTarget.getBoundingClientRect();
//       setPosition({
//         x: left + width,
//         y: props.bottom ? top - 80 : top,
//       });
//     };

//     return (
//       <div className="py-2">
//         {getTooltip()}
//         <Dropdown show={show} drop="right">
//           <DropdownToggle
//             title={props.buttonTitle}
//             ref={buttonRef}
//             variant="link"
//             className="bg-secondary px-0"
//             block
//             size="lg"
//             onClick={showMenu}
//             onMouseEnter={() => {
//               setTooltip(true);
//             }}
//             onMouseLeave={() => {
//               setTooltip(false);
//             }}
//           >
//             <DropdownLabel
//               title={props.title}
//               className={`m-auto ${props.isActive ? "btn-theme" : null}`}
//               isActive={props.isActive}
//             >
//               <div
//                 className={props.isActive ? "icon-wrap-active" : "icon-wrap"}
//               >
//                 {props.buttonContent}
//               </div>
//             </DropdownLabel>
//           </DropdownToggle>
//           {getDropdownMenuTemplate()}
//         </Dropdown>
//       </div>
//     );
//   } else {
//     return (
//       <div
//         onMouseEnter={() => {
//           setTooltip(true);
//         }}
//         onMouseLeave={() => {
//           setTooltip(false);
//         }}
//       >
//         {getTooltip()}
//         <Button
//           to={props.route}
//           as={props.as}
//           ref={buttonRef}
//           variant="link"
//           className="bg-secondary text-white py-2 px-0"
//           block
//           size="lg"
//         >
//           <DropdownLabel
//             className={`m-auto ${props.isActive ? "btn-theme" : null}`}
//             isActive={props.isActive}
//           >
//             <div className={props.isActive ? "icon-wrap-active" : "icon-wrap"}>
//               {props.buttonContent}
//             </div>
//           </DropdownLabel>
//         </Button>
//       </div>
//     );
//   }
// };

export default Sidebar;
