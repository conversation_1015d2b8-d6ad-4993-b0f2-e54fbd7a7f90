import React from "react";
import Select, { components } from "react-select";
import { FormGroup, FormControl } from "../index";
import Icon from "../../components/Icon";
import { Div } from "../../components";

const Option = (props: any) => {
  return (
    <div className="px-2 py-1">
      <components.Option {...props} className="rounded pt-1 pb-2">
        <FormGroup className="m-0">
          <FormControl.Checkbox>
            <FormControl.Checkbox.Label className="f14">
              {props.label}
            </FormControl.Checkbox.Label>
            <FormControl.Checkbox.Input
              checked={props.isSelected}
              onChange={() => null}
            />
          </FormControl.Checkbox>
        </FormGroup>
        {/* <input
          type="checkbox"
          checked={props.isSelected}
          onChange={(e) => null}
        />{" "}
        <label className="m-0">{props.label}</label> */}
      </components.Option>
    </div>
  );
};

const MultiValue = (props: any) => {
  return (
    <components.MultiValue {...props}>
      <span>{props.data.label}</span>
    </components.MultiValue>
  );
};

const ValueContainer = ({ children, ...props }: any) => {
  return (
    <components.ValueContainer {...props}>
      {!!children && <Icon icon=" ml-1 fal fa-search fs-12" />}
      <Div className="ml-1">{children}</Div>
    </components.ValueContainer>
  );
};
// this comp should not be passed as props in Multiselect, doing so causes the react-select to stop closing/hiding the dropdown on outside click, due to d-flex
const ValueContainerWrap = ({ children, ...props }: any) => {
  return (
    <components.ValueContainer {...props}>
      {!!children && (
        <Icon icon="ml-1 fal fa-search fs-12 position-absolute" />
      )}
      <Div className="ml-1 d-flex flex-wrap w-100 align-items-center pl-3">
        {children}
      </Div>
    </components.ValueContainer>
  );
};

export default function MultiSelect(props: any) {
  const {
    onInputChange,
    placeholder,
    closeMenuOnSelect = false,
    CustomOption,
    onSelect = () => undefined,
    styles,
    postfix,
    customValContainer,
    hasError,
    className = "",
    key = "",
    ref,
    value,
    onBlur,
    wrap,
  } = props;

  const customStyles = {
    multiValue: (provided: any) => ({
      ...provided,
      borderRadius: 5,
      overflow: "hidden",
    }),
    dropdownIndicator: (provided: any) => ({
      ...provided,
      padding: 5,
      color: "#000",
      fontWeight: 200,
    }),
    ...(!styles && {
      input: (provided: any) => ({
        ...provided,
        fontSize: 12,
        margin: "0 3px",
        height: 30,
      }),
    }),
    control: (provided: any) => ({
      ...provided,
      minHeight: 18,
      background: "#FCFCFC",
      ...(hasError && { border: "1px solid #ff8b67" }),
      // ...(hasError && { boxShadow: "0px 0px 6px #ff8b67" }),
    }),
    placeholder: () => ({
      fontSize: 12,
    }),
    multiValueLabel: (provided: any) => ({
      ...provided,
      fontSize: 11,
    }),
    valueContainer: (provided: any) => ({
      ...provided,
      padding: "0 3px",
    }),
    ...(styles && { ...styles }),
  };

  return (
    <Select
      {...(key && { key: `unique_select_key__${key}` })}
      ref={ref || null}
      className={className}
      closeMenuOnSelect={closeMenuOnSelect}
      styles={customStyles}
      isMulti={props.isMulti ?? true}
      components={{
        Option: CustomOption || Option,
        MultiValue,
        ClearIndicator: () => null,
        IndicatorSeparator: () => null,
        ...(postfix && { DropdownIndicator: postfix }),
        ...(customValContainer && {
          ValueContainer: wrap ? ValueContainerWrap : ValueContainer,
        }),
      }}
      placeholder={placeholder || "Select Value"}
      options={props.options}
      hideSelectedOptions={false}
      backspaceRemovesValue={false}
      onChange={onSelect}
      theme={(theme) => ({
        ...theme,
        borderRadius: 3,
        padding: 1,
        fontSize: 10,
        colors: {
          ...theme.colors,
          text: "#000",
          primary25: "rgba(81, 140, 255, 0.1)",
          primary: "rgba(81, 140, 255, 0.1)",
          danger: "#000",
          neutral10: "#D2E1FF",
        },
      })}
      {...(onInputChange && { onInputChange })}
      {...(hasError && { hasError })}
      {...(value && { value })}
      {...(onBlur && { onBlur })}
    />
  );
}
