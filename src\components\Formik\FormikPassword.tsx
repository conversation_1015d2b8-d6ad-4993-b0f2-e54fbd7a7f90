import React from "react";
import { isFieldInvalid, getFieldErrorMessage } from "../../utils/formik";
import FormControl from "../Form/FormControl/FormControl";
import FormGroup from "../Form/FormGroup/FormGroup";

interface Props {
  label?: string;
  key: string;
  placeholder?: string;
  formik: any;
}

export default function FormikPasswordInput(props: Props) {
  const { label, key, placeholder, formik } = props;

  return (
    <FormGroup className="pb-4">
      <FormGroup.Label>{label}</FormGroup.Label>
      <FormControl.Password
        name={key}
        isInvalid={isFieldInvalid(formik, key)}
        value={formik.values[key]}
        onBlur={formik.handleBlur}
        onChange={formik.handleChange}
        placeholder={placeholder}
      ></FormControl.Password>
      <FormGroup.InValidFeedback
        text={getFieldErrorMessage(formik, key)}
      ></FormGroup.InValidFeedback>
    </FormGroup>
  );
}
