import React, { ReactNode, useEffect } from "react";
// import { debounce } from "lodash";
// import util from "../../../utils";
import { useDebounce } from "react-use";

const FormikAutoSave = ({
  values,
  onSave,
  children,
  debounceTime,
}: // initialValues,
{
  values: any;
  initialValues?: any;
  onSave: Function;
  children: ReactNode;
  debounceTime?: number;
}) => {
  const [count, setCount] = React.useState(0);

  // const onAutoSave = useCallback(
  //   debounce((_values: any) => {
  //     if (util.deepCheck(initialValues || {}, _values)) return;
  //     onSave(_values);
  //   }, debounceTime || 500),
  //   []
  // );

  useEffect(() => {
    setCount((_count: any) => _count + 1);
  }, [values]);

  const onAutoSave = (_values: any) => {
    if (count > 1) {
      onSave(_values);
    }
  };

  useDebounce(
    () => {
      onAutoSave(values);
    },
    debounceTime || 500,
    [values]
  );

  return <>{children}</>;
};

export default FormikAutoSave;
