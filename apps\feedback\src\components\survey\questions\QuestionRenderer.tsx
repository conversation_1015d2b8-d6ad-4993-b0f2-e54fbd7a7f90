import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Button } from '@repo/ui/components/button';
import { Textarea } from '@repo/ui/components/textarea';
import { RadioGroup, RadioGroupItem } from '@repo/ui/components/radio-group';
import { Checkbox } from '@repo/ui/components/checkbox';
import { Label } from '@repo/ui/components/label';
import { Edit3 } from 'lucide-react';
import { SurveyQuestion } from '@/services/engagementSurveyService';

// Extend SurveyQuestion to include text property for instructions and comment fields
interface ExtendedSurveyQuestion extends SurveyQuestion {
  text?: string;
  feedback?: string;
  commentId?: string;
}

interface QuestionRendererProps {
  question: ExtendedSurveyQuestion;
  questionNumber: number;
  onResponseUpdate: (questionId: string, value: any, responseId?: string, shouldDelete?: boolean, onNewResponseId?: (newId: string) => void) => void;
  onCommentUpdate: (questionId: string, comment: string, commentId?: string, onNewResponseId?: (newId: string) => void) => void;
}

const QuestionRenderer: React.FC<QuestionRendererProps> = ({
  question,
  questionNumber,
  onResponseUpdate,
  onCommentUpdate
}) => {
  const [response, setResponse] = useState(question.response?.value || '');
  const [feedback, setFeedback] = useState(question.feedback || '');
  // Show feedback section by default if there's existing feedback
  const [showFeedback, setShowFeedback] = useState(Boolean(question.feedback));
  // Track the current response ID locally
  const [currentResponseId, setCurrentResponseId] = useState(question.response?.id || null);
  // Track the current comment ID locally
  const [currentCommentId, setCurrentCommentId] = useState(question.commentId || null);

  // Debounce timeout ref for QuestionInput types
  const debounceTimeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);

  useEffect(() => {
    setResponse(question.response?.value || '');
    setFeedback(question.feedback || '');
    setCurrentResponseId(question.response?.id || null);
    setCurrentCommentId(question.commentId || null);
    // Show feedback section if there's existing feedback
    setShowFeedback(Boolean(question.feedback));
    console.log('QuestionRenderer - useEffect updating state:', {
      questionId: question.id,
      responseValue: question.response?.value,
      responseId: question.response?.id,
      feedback: question.feedback,
      commentId: question.commentId,
      updatedCurrentResponseId: question.response?.id || null,
      updatedCurrentCommentId: question.commentId || null,
      showFeedback: Boolean(question.feedback)
    });
  }, [question.response?.value, question.response?.id, question.feedback, question.commentId]);

  // Cleanup debounce timeout on unmount
  useEffect(() => {
    return () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
    };
  }, []);

  // Debounced comment update function for QuestionInput types
  const debouncedCommentUpdate = useCallback((questionId: string, comment: string, commentId?: string) => {
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }

    debounceTimeoutRef.current = setTimeout(() => {
      // For POST requests (new comments), provide a callback to update the currentCommentId
      const onNewResponseId = !commentId ? (newId: string) => {
        console.log('QuestionRenderer - Received new comment response ID:', newId);
        setCurrentCommentId(newId);
      } : undefined;

      onCommentUpdate(questionId, comment, commentId, onNewResponseId);
    }, 1000); // 1 second debounce
  }, [onCommentUpdate]);

  const handleResponseChange = (newResponse: any) => {
    // For QuestionInput types, handle as comment responses with debouncing
    if (question.resourcetype === 'QuestionInput' || question.type === 'QuestionInput') {
      setResponse(newResponse);
      // Use debounced comment update for QuestionInput types
      debouncedCommentUpdate(question.id, newResponse, currentResponseId || undefined);
      return;
    }

    const hasExistingResponse = !!currentResponseId;

    // Handle checkbox arrays - if array becomes empty, trigger DELETE
    if (Array.isArray(newResponse)) {
      console.log('QuestionRenderer - handleResponseChange (checkbox array):', {
        questionId: question.id,
        currentResponse: response,
        newResponse,
        isEmpty: newResponse.length === 0,
        hasExistingResponse,
        currentResponseId
      });

      if (newResponse.length === 0 && hasExistingResponse) {
        // DELETE: All checkboxes unchecked and we have an existing response
        setResponse([]);
        setCurrentResponseId(null);
        console.log('🗑️ DELETE - All checkboxes unchecked:', {
          questionId: question.id,
          existingResponseId: currentResponseId,
          verb: 'DELETE'
        });
        onResponseUpdate(question.id, [], currentResponseId, true);
        return;
      }

      // POST or PATCH: Update checkbox selection
      setResponse(newResponse);
      const verb = hasExistingResponse ? 'PATCH' : 'POST';
      console.log(`${verb === 'POST' ? '📝' : '✏️'} ${verb} - Updating checkbox selection:`, {
        questionId: question.id,
        newResponse,
        currentResponseId,
        verb
      });

      const onNewResponseId = !hasExistingResponse ? (newId: string) => {
        console.log('QuestionRenderer - Received new response ID from POST (checkbox):', newId);
        setCurrentResponseId(newId);
      } : undefined;

      onResponseUpdate(question.id, newResponse, currentResponseId || undefined, false, onNewResponseId);
      return;
    }

    // Handle single-value responses (radio, rating, etc.)
    const isCurrentlySelected = response === newResponse;

    console.log('QuestionRenderer - handleResponseChange (single value):', {
      questionId: question.id,
      currentResponse: response,
      newResponse,
      isCurrentlySelected,
      hasExistingResponse,
      currentResponseId
    });

    if (isCurrentlySelected && hasExistingResponse) {
      // DELETE: Clicking on already selected option with existing response
      setResponse('');
      setCurrentResponseId(null); // Clear the response ID locally
      console.log('🗑️ DELETE - Unselecting option:', {
        questionId: question.id,
        existingResponseId: currentResponseId,
        verb: 'DELETE'
      });
      onResponseUpdate(question.id, '', currentResponseId, true);
      return;
    }

    // POST or PATCH: Selecting new option
    setResponse(newResponse);
    const verb = hasExistingResponse ? 'PATCH' : 'POST';
    console.log(`${verb === 'POST' ? '📝' : '✏️'} ${verb} - Selecting option:`, {
      questionId: question.id,
      newResponse,
      currentResponseId,
      verb,
      reason: hasExistingResponse ? 'has existing response' : 'no existing response'
    });

    // For POST requests, provide a callback to update the currentResponseId
    const onNewResponseId = !hasExistingResponse ? (newId: string) => {
      console.log('QuestionRenderer - Received new response ID from POST:', newId);
      setCurrentResponseId(newId);
    } : undefined;

    onResponseUpdate(question.id, newResponse, currentResponseId || undefined, false, onNewResponseId);
  };

  const handleFeedbackChange = (newFeedback: string) => {
    setFeedback(newFeedback);
    // Use debounced update for feedback comments with current comment ID
    debouncedCommentUpdate(question.id, newFeedback, currentCommentId || undefined);
  };

  const renderRatingScale = () => {
    // Get the options from the question or use default
    const options = question.options || {
      "1": "Scale - 1",
      "2": "Scale - 2",
      "3": "Scale - 3",
      "4": "Scale - 4",
      "5": "Scale - 5"
    };

    console.log('QuestionRenderer - Rating scale debug:', {
      questionId: question.id,
      options: question.options,
      is_reverse_scale: question.is_reverse_scale,
      resourcetype: question.resourcetype,
      optionsType: Array.isArray(question.options) ? 'array' : typeof question.options
    });

    // Handle both object and array formats for options
    let ratings;
    if (Array.isArray(question.options)) {
      // If options is an array, map it directly
      ratings = question.options.map((option: any, index: number) => ({
        value: option.value || (index + 1),
        label: option.label || option.text || `Scale - ${option.value || (index + 1)}`
      }));
      // Add "No basis" option
      ratings.push({ value: 0, label: 'No basis for rating' });
    } else {
      // If options is an object, create ratings array from it
      ratings = [
        { value: 5, label: options["5"] || 'Scale - 5' },
        { value: 4, label: options["4"] || 'Scale - 4' },
        { value: 3, label: options["3"] || 'Scale - 3' },
        { value: 2, label: options["2"] || 'Scale - 2' },
        { value: 1, label: options["1"] || 'Scale - 1' },
        { value: 0, label: 'No basis for rating' }
      ];
    }

    // Always show in descending order: 5,4,3,2,1,0 (high to low)
    // The is_reverse_scale flag doesn't change the display order for the UI
    // It's used for scoring/analysis purposes on the backend
    const displayRatings = ratings;

    console.log('QuestionRenderer - Display ratings:', displayRatings);

    return (
      <div className="mt-4">
        <div className="flex w-full gap-3">
          {displayRatings.map((rating) => (
            <div key={rating.value} className="flex-1">
              <button
                type="button"
                onClick={() => handleResponseChange(rating.value)}
                className={`w-full h-[60px] p-0 border rounded transition-colors ${
                  response === rating.value
                    ? 'bg-blue-500 text-white border-blue-500 dark:bg-blue-600 dark:border-blue-600'
                    : 'bg-white text-gray-700 hover:bg-gray-50 border-gray-300 dark:bg-gray-800 dark:text-gray-300 dark:hover:bg-gray-700 dark:border-gray-600'
                }`}
              >
                <div className="flex items-center w-full h-full">
                  <div className="flex items-center justify-center min-w-[30px] py-2 px-2 font-medium">
                    {rating.value}
                  </div>
                  <div className={`border-l px-3 py-2 flex-1 text-left text-sm ${
                    response === rating.value
                      ? 'border-blue-400 dark:border-blue-500'
                      : 'border-gray-300 dark:border-gray-600'
                  }`}>
                    {rating.value === 0 ? 'No basis for rating' : rating.label}
                  </div>
                </div>
              </button>
            </div>
          ))}
        </div>
      </div>
    );
  };

  const renderRadioOptions = () => {
    if (!question.options || !Array.isArray(question.options)) return null;

    const handleRadioClick = (optionValue: string) => {
      const isCurrentlySelected = response?.toString() === optionValue;
      const hasExistingResponse = !!currentResponseId;

      console.log('QuestionRenderer - Radio click:', {
        questionId: question.id,
        currentResponse: response,
        clickedValue: optionValue,
        isCurrentlySelected,
        hasExistingResponse,
        currentResponseId
      });

      if (isCurrentlySelected && hasExistingResponse) {
        // DELETE: Clicking on already selected option with existing response
        setResponse('');
        setCurrentResponseId(null); // Clear the response ID locally
        console.log('🗑️ DELETE - Unselecting radio option:', {
          questionId: question.id,
          existingResponseId: currentResponseId,
          verb: 'DELETE'
        });
        onResponseUpdate(question.id, '', currentResponseId, true);
      } else {
        // POST or PATCH: Selecting new option
        setResponse(optionValue);
        const verb = hasExistingResponse ? 'PATCH' : 'POST';
        console.log(`${verb === 'POST' ? '📝' : '✏️'} ${verb} - Selecting radio option:`, {
          questionId: question.id,
          value: optionValue,
          currentResponseId,
          verb,
          reason: hasExistingResponse ? 'has existing response' : 'no existing response'
        });

        // For POST requests, provide a callback to update the currentResponseId
        const onNewResponseId = !hasExistingResponse ? (newId: string) => {
          console.log('QuestionRenderer - Received new response ID from POST (radio):', newId);
          setCurrentResponseId(newId);
        } : undefined;

        onResponseUpdate(question.id, optionValue, currentResponseId || undefined, false, onNewResponseId);
      }
    };

    return (
      <RadioGroup
        value={response?.toString()}
        onValueChange={() => {}} // Disable default behavior, we handle clicks manually
        className="space-y-3"
      >
        {question.options.map((option, index) => {
          const optionValue = option.value?.toString() || option.text || option;
          return (
            <div
              key={option.id || index}
              className="flex items-center space-x-3 cursor-pointer"
              onClick={() => handleRadioClick(optionValue)}
            >
              <RadioGroupItem
                value={optionValue}
                id={`option-${option.id || index}`}
                className="pointer-events-none" // Prevent default radio behavior
              />
              <Label htmlFor={`option-${option.id || index}`} className="cursor-pointer pointer-events-none">
                {option.text || option.label || option}
              </Label>
            </div>
          );
        })}
      </RadioGroup>
    );
  };

  const renderCheckboxOptions = () => {
    if (!question.options || !Array.isArray(question.options)) return null;

    const selectedValues = Array.isArray(response) ? response : [];

    return (
      <div className="space-y-3">
        {question.options.map((option, index) => (
          <div key={option.id || index} className="flex items-center space-x-3">
            <Checkbox
              id={`checkbox-${option.id || index}`}
              checked={selectedValues.includes(option.value || option.text || option)}
              onCheckedChange={(checked) => {
                const optionValue = option.value || option.text || option;
                const newValues = checked
                  ? [...selectedValues, optionValue]
                  : selectedValues.filter(v => v !== optionValue);
                handleResponseChange(newValues);
              }}
            />
            <Label htmlFor={`checkbox-${option.id || index}`} className="cursor-pointer">
              {option.text || option.label || option}
            </Label>
          </div>
        ))}
      </div>
    );
  };



  const renderInput = () => (
    <Textarea
      value={response}
      onChange={(e) => handleResponseChange(e.target.value)}
      placeholder="Enter your response..."
      className="min-h-[100px]"
    />
  );

  const renderInstruction = () => (
    <div className="p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg">
      <p className="text-gray-700 dark:text-gray-300 text-sm leading-relaxed">
        {question.text}
      </p>
    </div>
  );

  const renderQuestionContent = () => {
    switch (question.resourcetype || question.type) {
      case 'QuestionRating':
        return renderRatingScale();
      case 'QuestionRadio':
        return renderRadioOptions();
      case 'QuestionCheckbox':
        return renderCheckboxOptions();
      case 'QuestionInput':
        return renderInput();
      case 'Paragraph':
        // Check if this is an instruction paragraph
        // Instructions typically have text content but no input capability
        // Based on legacy app: instructions have text but no options and aren't meant for user input
        const isInstructionParagraph = question.text &&
          question.text.trim().length > 0

        return isInstructionParagraph && renderInstruction();
      case 'Instruction':
      case 'QuestionInstruction':
      case 'InstructionText':
      case 'Text':
        return renderInstruction();
      default:
        return (
          <div className="p-4 border border-dashed rounded-lg text-center text-muted-foreground">
            Question type "{question.resourcetype || question.type}" not yet implemented
          </div>
        );
    }
  };

  // For instruction questions, render differently (no question number, no feedback)
  const questionType = question.resourcetype || question.type;
  const isInstructionQuestion = ['Instruction', 'QuestionInstruction', 'InstructionText', 'Text'].includes(questionType) ||
    (questionType === 'Paragraph' && question.text && question.text.trim().length > 0 && !question.options && !question.mandatory);

  if (isInstructionQuestion) {
    return (
      <div className="mb-6">
        {renderQuestionContent()}
      </div>
    );
  }

  return (
    <div className="mb-6 border rounded-lg p-4 border-gray-200 dark:border-gray-700">
      {/* Question Header */}
      <div className="mb-4 flex items-center justify-between">
        <h3 className="text-base font-medium text-gray-800 dark:text-white">
          {questionNumber}. {question.title || question.label}
          {question.mandatory && (
            <span className="text-red-500 dark:text-red-400 text-sm ml-1">*</span>
          )}
        </h3>
        {question.collect_feedback && (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowFeedback(!showFeedback)}
            className="text-blue-600 dark:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20 flex items-center gap-1"
          >
            <Edit3 className="h-4 w-4" />
            Comment
          </Button>
        )}
      </div>

      {/* Question Content */}
      <div className="mb-4">
        {renderQuestionContent()}
      </div>

      {/* Feedback Section */}
      {question.collect_feedback && showFeedback && (
        <div className="mt-4">
          <div className="space-y-2">
            <Label htmlFor={`feedback-${question.id}`} className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Additional Comments (Optional)
            </Label>
            <Textarea
              id={`feedback-${question.id}`}
              value={feedback}
              onChange={(e) => handleFeedbackChange(e.target.value)}
              placeholder="Share any additional thoughts or feedback..."
              className="min-h-[80px] border-gray-300 dark:border-gray-600"
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default QuestionRenderer;
