import React from "react";
import { cn } from "../lib/utils";

/**
 * Layout Components - Tailwind CSS implementation of Bootstrap-like grid system
 *
 * This module provides Container, Row, Col, Flex, and FlexItem components
 * that mimic the behavior of react-bootstrap components but use Tailwind CSS.
 *
 * Usage:
 * ```tsx
 * import Layout from "@repo/ui/components/layout";
 *
 * <Layout.Container>
 *   <Layout.Row>
 *     <Layout.Col xs={12} md={6}>Content 1</Layout.Col>
 *     <Layout.Col xs={12} md={6}>Content 2</Layout.Col>
 *   </Layout.Row>
 * </Layout.Container>
 * ```
 */

// Container Component
interface ContainerProps {
  children?: React.ReactNode;
  fluid?: boolean;
  className?: string;
  style?: React.CSSProperties;
}

const Container = React.forwardRef<HTMLDivElement, ContainerProps>(
  ({ children, fluid = false, className, style, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(
          fluid 
            ? "w-full px-4" 
            : "container mx-auto px-4 max-w-7xl",
          className
        )}
        style={style}
        {...props}
      >
        {children}
      </div>
    );
  }
);
Container.displayName = "Container";

// Row Component
interface RowProps {
  children?: React.ReactNode;
  className?: string;
  style?: React.CSSProperties;
  noGutters?: boolean;
}

const Row = React.forwardRef<HTMLDivElement, RowProps>(
  ({ children, className, style, noGutters = false, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(
          "flex flex-wrap",
          noGutters ? "" : "-mx-2",
          className
        )}
        style={style}
        {...props}
      >
        {children}
      </div>
    );
  }
);
Row.displayName = "Row";

// Col Component
interface ColProps {
  children?: React.ReactNode;
  className?: string;
  style?: React.CSSProperties;
  // Responsive breakpoints
  xs?: number | "auto";
  sm?: number | "auto";
  md?: number | "auto";
  lg?: number | "auto";
  xl?: number | "auto";
  // Offset props
  "offset-xs"?: number;
  "offset-sm"?: number;
  "offset-md"?: number;
  "offset-lg"?: number;
  "offset-xl"?: number;
}

const Col = React.forwardRef<HTMLDivElement, ColProps>(
  ({ 
    children, 
    className, 
    style, 
    xs, 
    sm, 
    md, 
    lg, 
    xl,
    "offset-xs": offsetXs,
    "offset-sm": offsetSm,
    "offset-md": offsetMd,
    "offset-lg": offsetLg,
    "offset-xl": offsetXl,
    ...props 
  }, ref) => {
    
    // Helper function to generate responsive classes
    const getResponsiveClasses = () => {
      const classes: string[] = ["px-2"]; // Default padding for gutters

      // Base column class
      if (!xs && !sm && !md && !lg && !xl) {
        classes.push("flex-1");
      }

      // Helper to get width class for a breakpoint
      const getWidthClass = (size: number | "auto", prefix = "") => {
        if (size === "auto") {
          return `${prefix}flex-auto`;
        }

        const prefixStr = prefix ? `${prefix}:` : "";

        // Map common Bootstrap column sizes to Tailwind classes
        switch (size) {
          case 1: return `${prefixStr}w-1/12`;
          case 2: return `${prefixStr}w-2/12`;
          case 3: return `${prefixStr}w-3/12`;
          case 4: return `${prefixStr}w-4/12`;
          case 5: return `${prefixStr}w-5/12`;
          case 6: return `${prefixStr}w-6/12`;
          case 7: return `${prefixStr}w-7/12`;
          case 8: return `${prefixStr}w-8/12`;
          case 9: return `${prefixStr}w-9/12`;
          case 10: return `${prefixStr}w-10/12`;
          case 11: return `${prefixStr}w-11/12`;
          case 12: return `${prefixStr}w-full`;
          default: return `${prefixStr}w-[${Math.round((size / 12) * 100)}%]`;
        }
      };

      // Helper to get offset class
      const getOffsetClass = (size: number, prefix = "") => {
        const prefixStr = prefix ? `${prefix}:` : "";

        switch (size) {
          case 1: return `${prefixStr}ml-[8.333333%]`;
          case 2: return `${prefixStr}ml-[16.666667%]`;
          case 3: return `${prefixStr}ml-[25%]`;
          case 4: return `${prefixStr}ml-[33.333333%]`;
          case 5: return `${prefixStr}ml-[41.666667%]`;
          case 6: return `${prefixStr}ml-[50%]`;
          case 7: return `${prefixStr}ml-[58.333333%]`;
          case 8: return `${prefixStr}ml-[66.666667%]`;
          case 9: return `${prefixStr}ml-[75%]`;
          case 10: return `${prefixStr}ml-[83.333333%]`;
          case 11: return `${prefixStr}ml-[91.666667%]`;
          default: return `${prefixStr}ml-[${Math.round((size / 12) * 100)}%]`;
        }
      };

      // Apply breakpoint classes
      if (xs) classes.push(getWidthClass(xs));
      if (sm) classes.push(getWidthClass(sm, "sm"));
      if (md) classes.push(getWidthClass(md, "md"));
      if (lg) classes.push(getWidthClass(lg, "lg"));
      if (xl) classes.push(getWidthClass(xl, "xl"));

      // Apply offset classes
      if (offsetXs) classes.push(getOffsetClass(offsetXs));
      if (offsetSm) classes.push(getOffsetClass(offsetSm, "sm"));
      if (offsetMd) classes.push(getOffsetClass(offsetMd, "md"));
      if (offsetLg) classes.push(getOffsetClass(offsetLg, "lg"));
      if (offsetXl) classes.push(getOffsetClass(offsetXl, "xl"));

      return classes.join(" ");
    };

    return (
      <div
        ref={ref}
        className={cn(
          getResponsiveClasses(),
          className
        )}
        style={style}
        {...props}
      >
        {children}
      </div>
    );
  }
);
Col.displayName = "Col";

// Flex Component
interface FlexProps {
  children?: React.ReactNode;
  className?: string;
  style?: React.CSSProperties;
  styleOptions?: any; // For backward compatibility with old app
}

const Flex = React.forwardRef<HTMLDivElement, FlexProps>(
  ({ children, className, style, styleOptions, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(
          "flex",
          className
        )}
        style={style}
        {...props}
      >
        {children}
      </div>
    );
  }
);
Flex.displayName = "Flex";

// FlexItem Component
interface FlexItemProps {
  children?: React.ReactNode;
  className?: string;
  style?: React.CSSProperties;
  styleOptions?: any; // For backward compatibility with old app
}

const FlexItem = React.forwardRef<HTMLDivElement, FlexItemProps>(
  ({ children, className, style, styleOptions, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(
          "text-left text-wrap break-words",
          className
        )}
        style={style}
        {...props}
      >
        {children}
      </div>
    );
  }
);
FlexItem.displayName = "FlexItem";

// Main Layout object that matches the old app structure
const Layout = {
  Container,
  Row,
  Col,
  Flex,
  FlexItem,
};

export default Layout;
export { Container, Row, Col, Flex, FlexItem };
