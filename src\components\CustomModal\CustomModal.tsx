import React from "react";
import { Modal as Md } from "react-bootstrap";
import styled from "styled-components";

const CustomModal = (props: any) => {
  return <Md {...props} />;
};

const Header = styled(Md.Header)`
  background: #f2f2f2;
  display: flex;
  justify-content: center;
  align-items: center;
  border: none;
  .modal-title {
    font-size: 14px;
    font-weight: 600;
  }

  button {
    background: #dadada;
    padding: 0px !important;
    width: 30px;
    height: 30px;
    font-size: 16px;
    margin-right: 0 !important;
    border-radius: 3px;
  }
`;
const Body = styled(Md.Body)`
  height: ${(props) => (props.size === "sm" ? "" : "61vh")};
  overflow: auto;
`;

const Footer = styled(Md.Footer)`
  border: none;
  min-height: 66px;
`;
CustomModal.Title = Md.Title;
CustomModal.Header = Header;
CustomModal.Body = Body;
CustomModal.Footer = Footer;

export default CustomModal;
