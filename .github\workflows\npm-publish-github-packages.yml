name: Package Release Workflow

on:
  push:
    branches:
      - master
      - development
      - review

jobs:
  publish:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout Code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20
          registry-url: https://npm.pkg.github.com

      - name: Install Dependencies
        run: npm install

      - name: Build Project
        run: npm run build

      - name: Release Package
        env:
          GITHUB_TOKEN: ${{ secrets.AUTH_TOKEN }}
          NPM_TOKEN: ${{ secrets.AUTH_TOKEN }}
        run: npx semantic-release

      - name: Authenticate to GitHub Packages registry
        run: |
          echo "//npm.pkg.github.com/:_authToken=${{ secrets.AUTH_TOKEN }}" > ~/.npmrc
          echo "@unmatchedoffl:registry=https://npm.pkg.github.com" >> ~/.npmrc
        env:
          AUTH_TOKEN: ${{ secrets.AUTH_TOKEN }}

      - name: Publish to GitHub Packages
        run: npm publish
        env:
          NODE_AUTH_TOKEN: ${{ secrets.AUTH_TOKEN }}
