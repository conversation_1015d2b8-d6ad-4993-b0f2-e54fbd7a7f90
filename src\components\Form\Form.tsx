import React from "react";

export type FormProps = React.HTMLAttributes<HTMLFormElement>;

const Form = (props: FormProps) => {
  const { onSubmit, children, onReset, className } = props;
  const onFormSubmit = (e: any) => {
    e.stopPropagation();
    e.preventDefault();
    if (onSubmit) onSubmit(e);
  };
  const onFormReset = (e: any) => {
    e.stopPropagation();
    e.preventDefault();
    if (onReset) onReset(e);
  };
  return (
    <form onSubmit={onFormSubmit} onReset={onFormReset} className={className}>
      {children}
    </form>
  );
};

Form.defaultProps = {
  className: "",
};

export default Form;
