import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@repo/ui/components/button';
import { Input } from '@repo/ui/components/input';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@repo/ui/components/form';
import { Alert, AlertDescription } from '@repo/ui/components/alert';
import { changePasswordFact } from '@/services/authService';
import useSession from '@/unmatched/modules/session/hook';
import { useNavigate } from 'react-router';

interface ChangePasswordProps {
  closeModal: () => void;
}

type ChangePasswordFormData = {
  oldPassword?: string;
  password: string;
  confirmPassword: string;
};

const ChangePassword: React.FC<ChangePasswordProps> = ({ closeModal }) => {
  const { user } = useSession();
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  const isPasswordSet = user?.isPasswordSet;

  // Create schema based on whether password is set
  const schema = isPasswordSet
    ? z
        .object({
          oldPassword: z.string().min(8, 'Old password must be at least 8 characters'),
          password: z.string().min(8, 'Password must be at least 8 characters'),
          confirmPassword: z.string().min(8, 'Confirm password must be at least 8 characters'),
        })
        .refine((data) => data.password === data.confirmPassword, {
          message: "Passwords do not match",
          path: ["confirmPassword"],
        })
        .refine((data) => data.oldPassword !== data.password, {
          message: "New password cannot be the same as old password",
          path: ["password"],
        })
    : z
        .object({
          password: z.string().min(8, 'Password must be at least 8 characters'),
          confirmPassword: z.string().min(8, 'Confirm password must be at least 8 characters'),
        })
        .refine((data) => data.password === data.confirmPassword, {
          message: "Passwords do not match",
          path: ["confirmPassword"],
        });

  // Create form with proper default values
  const form = useForm<ChangePasswordFormData>({
    defaultValues: {
      oldPassword: isPasswordSet ? "" : undefined,
      password: "",
      confirmPassword: "",
    },
    resolver: zodResolver(schema),
  });

  const onSubmit = async (values: ChangePasswordFormData) => {
    setIsLoading(true);
    setError(null);

    try {
      await changePasswordFact({
        oldPassword: values.oldPassword,
        password: values.password,
      });

      setSuccess(true);
      
      // Show success message briefly then redirect to logout
      setTimeout(() => {
        closeModal();
        navigate('/logout');
      }, 2000);

    } catch (err: any) {
      console.error('Error changing password:', err);
      setError(err.response?.data?.message || 'Failed to change password. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  if (success) {
    return (
      <div className="p-6">
        <Alert>
          <AlertDescription>
            Password changed successfully! You will be logged out in a moment.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="p-6">
      {error && (
        <Alert variant="destructive" className="mb-4">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
          {isPasswordSet && (
            <FormField
              control={form.control}
              name="oldPassword"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Old Password</FormLabel>
                  <FormControl>
                    <Input 
                      {...field} 
                      type="password" 
                      placeholder="Enter old password"
                      disabled={isLoading}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          )}

          <FormField
            control={form.control}
            name="password"
            render={({ field }) => (
              <FormItem>
                <FormLabel>New Password</FormLabel>
                <FormControl>
                  <Input 
                    {...field} 
                    type="password" 
                    placeholder="Enter new password"
                    disabled={isLoading}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="confirmPassword"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Confirm New Password</FormLabel>
                <FormControl>
                  <Input 
                    {...field} 
                    type="password" 
                    placeholder="Confirm new password"
                    disabled={isLoading}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="flex justify-end gap-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={closeModal}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isLoading}
            >
              {isLoading ? 'Changing...' : (isPasswordSet ? 'Change Password' : 'Set Password')}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
};

export default ChangePassword;
