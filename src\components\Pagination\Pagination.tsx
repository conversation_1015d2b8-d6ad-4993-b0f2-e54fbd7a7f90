import React from "react";
import _ from "lodash";
import Button from "../Button/Button";
import Icon from "../Icon";
// import "./Pagination.scss";

interface PaginationProps {
  pages?: number;
  size?: "sm" | "lg";
  variant?: "table" | "toolbar";
  placement: "start" | "center" | "end";
  active: number;
  onSelect?: Function;
  pageSize: number;
  totalItems?: number;
}

const Pagination = (props: PaginationProps) => {
  const { pages, size, placement, active, onSelect, totalItems, pageSize } =
    props;
  if (!pages) return null;
  const MAX_PAGE = 4;
  const sizeClass = `pagination-${size}`;
  const list = _.range(1, pages + 1);
  const isHeavy = list.length > MAX_PAGE;
  const [selectedList, setSelectedList]: any = React.useState([]);

  const onPageSelect = (item: number) => {
    if (onSelect) onSelect(item);
  };

  const onRight = () => {
    if (active && active < pages) {
      onPageSelect(active + 1);
    }
  };

  const onLeft = () => {
    if (active && active > 1) {
      onPageSelect(active - 1);
    }
  };

  const getPagesTemplate = () => {
    let _list: any = list;
    if (isHeavy) {
      if (selectedList.includes(active)) {
        _list = selectedList;
      } else {
        const activeIndex = active - 1;
        const lastIndex = list.length - MAX_PAGE;
        if (activeIndex > lastIndex) {
          // debugger;
          _list = _list.slice(lastIndex, list.length);
        } else {
          _list = _list.slice(activeIndex, MAX_PAGE + activeIndex);
        }
        setSelectedList(_list);
      }
    }
    const cb = (item: number) => {
      const activeClass =
        active === item
          ? "page-item active border-right"
          : "page-item border-right";

      const onItemSelect = () => {
        onPageSelect(item);
      };

      return (
        <li key={item} className={activeClass}>
          <Button onClick={onItemSelect} className="text-dark" variant="link">
            {item}
          </Button>
        </li>
      );
    };
    return _list.map(cb);
  };
  return (
    <nav className="Custom-Pagination d-flex flex-row justify-content-between">
      <p className="m-0 fw-600 fs-12 text-muted pt-2">
        Showing {pageSize * (active - 1) + 1} -{" "}
        {active && active < pages
          ? pageSize * (active - 1) + pageSize
          : totalItems}{" "}
        of {totalItems} results
      </p>
      <ul
        className={`${"pagination"} ${sizeClass} justify-content-${placement} border`}
      >
        {active && active > 1 && (
          <>
            <li className={"page-item"}>
              <Button onClick={() => {
                onPageSelect(1);
              }} className="text-dark" variant="link">
                <Icon icon="far fa-chevron-double-left"></Icon>
              </Button>
            </li>
            <li className={"page-item border-right"}>
              <Button onClick={onLeft} className="text-dark" variant="link">
                {/* Previous */}
                <Icon icon="far fa-chevron-left"></Icon>
              </Button>
            </li>
          </>
        )}
        {getPagesTemplate()}
        {active && active < pages && (
          <>
            <li className={"page-item"}>
              <Button onClick={onRight} className="text-dark" variant="link">
                {/* Next */}
                <Icon icon="far fa-chevron-right"></Icon>
              </Button>
            </li>
            <li className={"page-item"}>
              <Button onClick={() => {
                onPageSelect(pages);
              }} className="text-dark" variant="link">
                <Icon icon="far fa-chevron-double-right"></Icon>
              </Button>
            </li>
          </>
        )}
      </ul>
    </nav>
  );
};

Pagination.defaultProps = {
  pages: 0,
  active: 1,
  size: "sm",
  placement: "end",
};

export default Pagination;
