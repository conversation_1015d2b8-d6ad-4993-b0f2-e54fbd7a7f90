
// APPS MODULE CSS START

.fade.modal-backdrop.custom-backdrop.show {
    opacity: 0;
  }
  
  .modal-dialog.apps-modal {
    margin: 0px;
    width: 310px;
  
    .modal-content {
      height: 545px;
      background: #1B2945;
      box-shadow: 0px 4px 40px 0px rgba(0, 0, 0, 0.25);
    }
  
    .modal-body {
      padding: 0px;
    }
  
    .modal-footer {
      border-top: none;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
  
  .apps-modal-wrap.modal.show {
    padding-left: 0px !important;
    z-index: 1111;
    backdrop-filter: unset;
  }
  
  .apps-text {
    color: #FFF;
    font-family: Inter;
    font-size: 10px;
    font-style: normal;
    font-weight: 600;
    line-height: 18px;
    opacity: 0.5;
    margin-bottom: 12px;
  }
  
  .app-desc {
    color: #FFF;
    font-family: Inter;
    font-size: 11px;
    font-style: normal;
    font-weight: 300;
    line-height: 18px;
    opacity: 0.5;
    margin-top: 2px;
  }
  
  .app-name {
    color: #FFF;
    font-family: Inter;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 13px; /* 128.571% */
  }
  
  .feat-link {
    color: #FFF;
    font-family: Inter;
    font-size: 13px;
    font-style: normal;
    font-weight: 400;
    line-height: 18px; /* 138.462% */
    display: flex;
    align-items: center;
    height: 40px;
    cursor: pointer;
    padding-left: 35px;
  }
  
  .feat-link:hover {
    background-color: rgba(255, 255, 255, 0.1) !important;
    box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.15);
    border-radius: 2px;
    color: #fff;
  }
  
  .color-block {
    width: 24px;
    height: 24px;
    flex-shrink: 0;
    border-radius: 2px;
    margin-right: 10px;
  }
  
  .app-btn {
    width: 266px;
  height: 34px;
  flex-shrink: 0;
  border-radius: 4px;
  background: #414c63;
  color: #FFF;
  
  font-family: Inter;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 18px;
  display: flex;
      align-items: center;
  justify-content: center;
  cursor: pointer;
  }
  
  .app-btn:hover {
    color: #fff
  }