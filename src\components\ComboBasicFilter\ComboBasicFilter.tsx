import React from "react";
import Div from "../Div";
import FormControl from "../Form/FormControl/FormControl";
import Icon from "../Icon";
// import Layout from "../Layout/Layout";
interface Props {
  cohart: any;
  applied: any;
  onCohartUpdate: Function;
  onAppliedUpdate: Function;
  hideIcon?: any;
  isAppliedShown: boolean;
}

const ComboFilter = (props: Props) => {
  const {
    cohart,
    applied,
    onCohartUpdate,
    onAppliedUpdate,
    hideIcon,
    isAppliedShown,
  } = props;

  return (
    <Div className="row">
      {!hideIcon && (
        <Div className="pr-0 pt-1 col" style={{ maxWidth: 30 }}>
          <Icon icon="far fa-filter" />
        </Div>
      )}
      <Div className={`${isAppliedShown ? "col-6 pr-0 " : "col"}`}>
        <FormControl.Select
          variant="white"
          value={
            cohart.selected && cohart.selected.length > 0 ? cohart.selected : ""
          }
        >
          {cohart.options.map((item: any, index: number) => {
            return (
              <FormControl.SelectItem
                onSelect={() => {
                  onCohartUpdate(item.key);
                }}
                key={index}
              >
                {item.title}
              </FormControl.SelectItem>
            );
          })}
        </FormControl.Select>
      </Div>
      {isAppliedShown && (
        <Div className="col-5 pr-0">
          <FormControl.Select variant="white" value={applied.selected ?? "-"}>
            {applied.options.map((item: any, index: number) => {
              return (
                <FormControl.SelectItem
                  onSelect={() => {
                    onAppliedUpdate(item);
                  }}
                  key={index}
                >
                  {item.title}
                </FormControl.SelectItem>
              );
            })}
          </FormControl.Select>
        </Div>
      )}
    </Div>
  );
};

export default ComboFilter;
