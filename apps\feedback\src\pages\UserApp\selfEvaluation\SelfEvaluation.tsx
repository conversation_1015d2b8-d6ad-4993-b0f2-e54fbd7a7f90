import React from 'react';
import { useParams } from 'react-router';
import { Alert, AlertDescription } from '@repo/ui/components/alert';
// import SurveyHeader from './components/SurveyHeader';
import SurveySidebar from './components/SurveySidebar';
import SurveyContent from './components/SurveyContent';
import SubmitConfirmation from './components/SubmitConfirmation';
import { useSurveyData } from './hooks/useSurveyData';
import { useSurveyNavigation } from './hooks/useSurveyNavigation';
import { useSurveyAutoSave } from './hooks/useSurveyAutoSave';

const SelfEvaluation: React.FC = () => {
  const { id } = useParams<{ id: string }>();

  // Custom hooks for data management
  const {
    isLoading,
    surveyData,
    currentSection,
    error,
    setCurrentSection,
    setError,
    fetchSurveyData,
    setSurveyData
  } = useSurveyData();

  // Custom hooks for auto-save functionality
  const {
    isSaving,
    lastSaved,
    handleResponseUpdate,
    handleCommentUpdate
  } = useSurveyAutoSave(surveyData, setSurveyData);

  // Custom hooks for navigation
  const {
    confirmSubmit,
    isSubmitting,
    setConfirmSubmit,
    handleNext,
    handleBack,
    handleSubmit,
    handleSectionSelect
  } = useSurveyNavigation();

  // Initialize survey data
  React.useEffect(() => {
    if (id) {
      fetchSurveyData(id);
    }
  }, [id, fetchSurveyData]);

  // Handle response updates with optimistic updates
  const onResponseUpdate = (questionId: string, value: any, responseId?: string, shouldDelete?: boolean) => {
    if (!surveyData) return;
    handleResponseUpdate(surveyData.responseId, questionId, value, responseId, shouldDelete);
  };

  // Handle comment updates
  const onCommentUpdate = (questionId: string, comment: string, commentId?: string, onNewResponseId?: (newId: string) => void) => {
    if (!surveyData) return;
    handleCommentUpdate(surveyData.responseId, questionId, comment, commentId, onNewResponseId);
  };

  // Handle navigation actions
  const onNext = () => handleNext(currentSection, surveyData, setCurrentSection);
  const onBack = () => handleBack(currentSection, surveyData, setCurrentSection);
  const onSectionSelect = (section: any) => handleSectionSelect(section, setCurrentSection);
  const onSubmit = async () => {
    try {
      await handleSubmit(surveyData);
    } catch (err) {
      setError('Failed to submit survey');
    }
  };

  if (error) {
    return (
      <div className="p-6 bg-white dark:bg-gray-900">
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      </div>
    );
  }

  if (confirmSubmit) {
    return (
      <SubmitConfirmation
        isSubmitting={isSubmitting}
        onBack={() => setConfirmSubmit(false)}
        onSubmit={onSubmit}
      />
    );
  }

  return (
    <div className="h-full bg-gray-50 dark:bg-gray-900 w-full flex flex-col overflow-hidden">
      {/* Fixed Header - Orange bar like legacy app */}
      <div className="bg-orange-400 dark:bg-orange-600 px-6 py-4 border-b dark:border-gray-700 flex-shrink-0">
        <div className="flex justify-between items-center">
          <h1 className="text-lg font-medium text-white">
            {surveyData?.meta?.title || 'Self Evaluation'}
          </h1>
          <div className="flex items-center space-x-4 text-sm text-white">
            <span>{surveyData?.completion || 0}% Completed</span>
            <span>•</span>
            <span>
              {isSaving ? 'Saving...' : `Saved ${lastSaved.toLocaleDateString('en-US', {
                month: 'short',
                day: 'numeric',
                year: 'numeric',
                hour: 'numeric',
                minute: '2-digit',
                hour12: true
              })}`}
            </span>
          </div>
        </div>
      </div>

      {/* Main content area with sidebar and content */}
      <div className="flex flex-1 overflow-hidden">
        {/* Fixed Sidebar */}
        <SurveySidebar
          isLoading={isLoading}
          surveyData={surveyData}
          currentSection={currentSection}
          onSectionSelect={onSectionSelect}
        />

        {/* Scrollable Main Content */}
        <SurveyContent
          isLoading={isLoading}
          isSaving={isSaving}
          surveyData={surveyData}
          currentSection={currentSection}
          lastSaved={lastSaved}
          onResponseUpdate={onResponseUpdate}
          onCommentUpdate={onCommentUpdate}
          onNext={onNext}
          onBack={onBack}
        />
      </div>
    </div>
  );
};

export default SelfEvaluation;
