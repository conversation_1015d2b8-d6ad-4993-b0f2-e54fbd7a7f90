// import { ADMIN_URLS } from "@repo/app-urls";
import ADMIN_URLS from "@/app.routes";
import AdminLayout from "./Admin.layout";
import SurveyRoute from "./Survey/survey.route";
// import Survey from "./Survey";

const AdminRoute = [
  {
    path: ADMIN_URLS().default,
    element: <AdminLayout />,
    children: [
      //   {
      //     path: ADMIN_URLS().SURVEY.default,
      //     element: <>Survey </>,
      //   },
      ...SurveyRoute,
      {
        path: ADMIN_URLS().EMAIL,
        element: <>Email </>,
      },
      {
        path: ADMIN_URLS().ANALYTICS.default,
        element: <>Analytics </>,
      },
      {
        path: ADMIN_URLS().REPORTS.default,
        element: <>Reports </>,
      },
      {
        path: ADMIN_URLS().DATALOAD.default,
        element: <>Data Load </>,
      },
    ],
  },
];
export default AdminRoute;
