import React from 'react';
import { useNavigate } from 'react-router';
import { Button } from '@repo/ui/components/button';
import { USER_ROUTES } from '@/app.routes';
import { type SelfEvaluationSurveyData } from '@/services/selfEvaluationService';

interface SurveyHeaderProps {
  isLoading: boolean;
  isSaving: boolean;
  surveyData: SelfEvaluationSurveyData | null;
  lastSaved: Date;
}

const SurveyHeader: React.FC<SurveyHeaderProps> = ({
  isLoading,
  isSaving,
  surveyData,
  lastSaved
}) => {
  const navigate = useNavigate();

  return (
    <div className="bg-green-500 text-white px-6 py-4 w-full">
      <div className="flex items-center justify-between w-full">
        <div className="flex items-center space-x-4">
          <Button
            variant="ghost"
            onClick={() => navigate(USER_ROUTES().dashboard.surveyList)}
            className="text-white hover:bg-green-600 p-1"
          >
            ← Back
          </Button>
          <h1 className="text-xl font-medium">
            {isLoading ? (
              <div className="h-6 w-64 bg-green-400 rounded animate-pulse"></div>
            ) : (
              surveyData?.meta.title
            )}
          </h1>
        </div>
        <div className="text-right">
          <span className="text-sm">
            {surveyData?.completion || 0}% Complete
          </span>
          <span className="ml-4 text-sm">
            {isSaving ? 'Saving...' : `Saved ${lastSaved.toLocaleDateString('en-US', {
              month: 'short',
              day: 'numeric',
              year: 'numeric',
              hour: '2-digit',
              minute: '2-digit'
            })}`}
          </span>
        </div>
      </div>
    </div>
  );
};

export default SurveyHeader;
