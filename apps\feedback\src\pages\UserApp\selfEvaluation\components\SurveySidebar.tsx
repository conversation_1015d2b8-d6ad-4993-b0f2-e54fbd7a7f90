import React from 'react';
import { type SelfEvaluationSurveyData, type SurveySection } from '@/services/selfEvaluationService';

interface SurveySidebarProps {
  isLoading: boolean;
  surveyData: SelfEvaluationSurveyData | null;
  currentSection: SurveySection | null;
  onSectionSelect: (section: SurveySection) => void;
}

const SurveySidebar: React.FC<SurveySidebarProps> = ({
  isLoading,
  surveyData,
  currentSection,
  onSectionSelect
}) => {
  return (
    <div className="w-48 bg-gray-100 dark:bg-gray-800 h-full p-4 border-r dark:border-gray-700 flex-shrink-0 overflow-y-auto">
      {/* Survey End Date */}
      <div className="mb-6">
        <h3 className="text-sm font-medium text-gray-700 mb-1">Survey Ends On</h3>
        <p className="text-sm text-gray-600">
          {isLoading ? (
            <div className="h-4 w-32 bg-gray-300 rounded animate-pulse"></div>
          ) : (
            surveyData?.meta.endDate ?
              new Date(surveyData.meta.endDate).toLocaleDateString('en-US', {
                month: 'short',
                day: 'numeric',
                year: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
              }) : 'Not set'
          )}
        </p>
      </div>

      {/* Survey Status */}
      <div className="mb-6">
        <h3 className="text-sm font-medium text-gray-700 mb-2">Survey Status</h3>
        <div className="space-y-1">
          <div className="flex justify-between text-sm">
            <span className="text-gray-600">{surveyData?.questions.length || 0}</span>
            <span className="text-gray-500">Questions</span>
          </div>
          <div className="flex justify-between text-sm">
            <span className="text-green-600 font-medium">
              {Math.round(((surveyData?.completion || 0) / 100) * (surveyData?.questions.length || 0))}
            </span>
            <span className="text-gray-500">Completed</span>
          </div>
        </div>
      </div>

      {/* Categories */}
      <div>
        <h3 className="text-sm font-medium text-gray-700 mb-2">Categories</h3>
        <div className="space-y-1">
          {isLoading ? (
            Array(3).fill(0).map((_, index) => (
              <div key={index} className="h-6 w-full bg-gray-300 rounded animate-pulse mb-1"></div>
            ))
          ) : (
            surveyData?.sections.map((section) => {
              const sectionQuestions = section.questions;
              const completedQuestions = sectionQuestions.filter(q =>
                q.response?.value !== undefined && q.response?.value !== null && q.response?.value !== ''
              );
              const questionsLeft = sectionQuestions.length - completedQuestions.length;

              return (
                <div
                  key={section.id}
                  className={`p-2 rounded cursor-pointer text-sm ${
                    currentSection?.id === section.id
                      ? 'bg-green-100 text-green-800 border border-green-200'
                      : 'hover:bg-gray-200 text-gray-700'
                  }`}
                  onClick={() => onSectionSelect(section)}
                >
                  <div className="font-medium">{section.title}</div>
                  {questionsLeft > 0 && (
                    <div className="text-xs text-gray-500 mt-1">
                      {questionsLeft} questions left
                    </div>
                  )}
                </div>
              );
            })
          )}
        </div>
      </div>
    </div>
  );
};

export default SurveySidebar;
