import React from "react";
import Layout from "@repo/ui/components/layout";

const LayoutDemo: React.FC = () => {
  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
      <div className="max-w-7xl mx-auto px-4">
        <h1 className="text-4xl font-bold text-center mb-8 text-gray-900 dark:text-white">
          Layout Components Demo
        </h1>
        
        {/* Container Demo */}
        <section className="mb-12">
          <h2 className="text-2xl font-semibold mb-4 text-gray-800 dark:text-gray-200">
            Container Component
          </h2>
          
          <div className="mb-6">
            <h3 className="text-lg font-medium mb-2 text-gray-700 dark:text-gray-300">
              Regular Container (max-width with centering)
            </h3>
            <Layout.Container className="bg-blue-100 dark:bg-blue-900 border-2 border-blue-300 dark:border-blue-700 rounded-lg p-4">
              <p className="text-gray-800 dark:text-gray-200">
                This is a regular container with max-width and auto margins for centering.
                It has responsive breakpoints and padding.
              </p>
            </Layout.Container>
          </div>
          
          <div className="mb-6">
            <h3 className="text-lg font-medium mb-2 text-gray-700 dark:text-gray-300">
              Fluid Container (full width)
            </h3>
            <Layout.Container fluid className="bg-green-100 dark:bg-green-900 border-2 border-green-300 dark:border-green-700 rounded-lg p-4">
              <p className="text-gray-800 dark:text-gray-200">
                This is a fluid container that takes the full width of its parent.
                Perfect for full-width layouts.
              </p>
            </Layout.Container>
          </div>
        </section>

        {/* Row and Col Demo */}
        <section className="mb-12">
          <h2 className="text-2xl font-semibold mb-4 text-gray-800 dark:text-gray-200">
            Row and Column System
          </h2>
          
          <Layout.Container>
            <div className="mb-6">
              <h3 className="text-lg font-medium mb-2 text-gray-700 dark:text-gray-300">
                Equal Width Columns
              </h3>
              <Layout.Row>
                <Layout.Col className="bg-purple-100 dark:bg-purple-900 border border-purple-300 dark:border-purple-700 rounded p-3 m-1">
                  <p className="text-sm text-gray-800 dark:text-gray-200">Column 1</p>
                </Layout.Col>
                <Layout.Col className="bg-purple-100 dark:bg-purple-900 border border-purple-300 dark:border-purple-700 rounded p-3 m-1">
                  <p className="text-sm text-gray-800 dark:text-gray-200">Column 2</p>
                </Layout.Col>
                <Layout.Col className="bg-purple-100 dark:bg-purple-900 border border-purple-300 dark:border-purple-700 rounded p-3 m-1">
                  <p className="text-sm text-gray-800 dark:text-gray-200">Column 3</p>
                </Layout.Col>
              </Layout.Row>
            </div>

            <div className="mb-6">
              <h3 className="text-lg font-medium mb-2 text-gray-700 dark:text-gray-300">
                Responsive Columns (12-column grid)
              </h3>
              <Layout.Row>
                <Layout.Col xs={12} md={8} className="bg-yellow-100 dark:bg-yellow-900 border border-yellow-300 dark:border-yellow-700 rounded p-3 m-1">
                  <p className="text-sm text-gray-800 dark:text-gray-200">
                    xs=12 md=8 (Full width on mobile, 8/12 on medium+)
                  </p>
                </Layout.Col>
                <Layout.Col xs={12} md={4} className="bg-yellow-100 dark:bg-yellow-900 border border-yellow-300 dark:border-yellow-700 rounded p-3 m-1">
                  <p className="text-sm text-gray-800 dark:text-gray-200">
                    xs=12 md=4 (Full width on mobile, 4/12 on medium+)
                  </p>
                </Layout.Col>
              </Layout.Row>
            </div>

            <div className="mb-6">
              <h3 className="text-lg font-medium mb-2 text-gray-700 dark:text-gray-300">
                Complex Responsive Layout
              </h3>
              <Layout.Row>
                <Layout.Col xs={12} sm={6} md={4} lg={3} className="bg-red-100 dark:bg-red-900 border border-red-300 dark:border-red-700 rounded p-3 m-1">
                  <p className="text-xs text-gray-800 dark:text-gray-200">
                    xs=12 sm=6 md=4 lg=3
                  </p>
                </Layout.Col>
                <Layout.Col xs={12} sm={6} md={4} lg={3} className="bg-red-100 dark:bg-red-900 border border-red-300 dark:border-red-700 rounded p-3 m-1">
                  <p className="text-xs text-gray-800 dark:text-gray-200">
                    xs=12 sm=6 md=4 lg=3
                  </p>
                </Layout.Col>
                <Layout.Col xs={12} sm={6} md={4} lg={3} className="bg-red-100 dark:bg-red-900 border border-red-300 dark:border-red-700 rounded p-3 m-1">
                  <p className="text-xs text-gray-800 dark:text-gray-200">
                    xs=12 sm=6 md=4 lg=3
                  </p>
                </Layout.Col>
                <Layout.Col xs={12} sm={6} md={12} lg={3} className="bg-red-100 dark:bg-red-900 border border-red-300 dark:border-red-700 rounded p-3 m-1">
                  <p className="text-xs text-gray-800 dark:text-gray-200">
                    xs=12 sm=6 md=12 lg=3
                  </p>
                </Layout.Col>
              </Layout.Row>
            </div>

            <div className="mb-6">
              <h3 className="text-lg font-medium mb-2 text-gray-700 dark:text-gray-300">
                Columns with Offsets
              </h3>
              <Layout.Row>
                <Layout.Col md={4} className="bg-indigo-100 dark:bg-indigo-900 border border-indigo-300 dark:border-indigo-700 rounded p-3 m-1">
                  <p className="text-sm text-gray-800 dark:text-gray-200">md=4</p>
                </Layout.Col>
                <Layout.Col md={4} offset-md={4} className="bg-indigo-100 dark:bg-indigo-900 border border-indigo-300 dark:border-indigo-700 rounded p-3 m-1">
                  <p className="text-sm text-gray-800 dark:text-gray-200">md=4 offset-md=4</p>
                </Layout.Col>
              </Layout.Row>
              <Layout.Row>
                <Layout.Col md={3} offset-md={3} className="bg-indigo-100 dark:bg-indigo-900 border border-indigo-300 dark:border-indigo-700 rounded p-3 m-1">
                  <p className="text-sm text-gray-800 dark:text-gray-200">md=3 offset-md=3</p>
                </Layout.Col>
                <Layout.Col md={3} offset-md={3} className="bg-indigo-100 dark:bg-indigo-900 border border-indigo-300 dark:border-indigo-700 rounded p-3 m-1">
                  <p className="text-sm text-gray-800 dark:text-gray-200">md=3 offset-md=3</p>
                </Layout.Col>
              </Layout.Row>
            </div>

            <div className="mb-6">
              <h3 className="text-lg font-medium mb-2 text-gray-700 dark:text-gray-300">
                Auto-width Columns
              </h3>
              <Layout.Row>
                <Layout.Col md="auto" className="bg-teal-100 dark:bg-teal-900 border border-teal-300 dark:border-teal-700 rounded p-3 m-1">
                  <p className="text-sm text-gray-800 dark:text-gray-200">Auto width content</p>
                </Layout.Col>
                <Layout.Col md={6} className="bg-teal-100 dark:bg-teal-900 border border-teal-300 dark:border-teal-700 rounded p-3 m-1">
                  <p className="text-sm text-gray-800 dark:text-gray-200">md=6</p>
                </Layout.Col>
                <Layout.Col md="auto" className="bg-teal-100 dark:bg-teal-900 border border-teal-300 dark:border-teal-700 rounded p-3 m-1">
                  <p className="text-sm text-gray-800 dark:text-gray-200">Auto width</p>
                </Layout.Col>
              </Layout.Row>
            </div>
          </Layout.Container>
        </section>

        {/* Flex Components Demo */}
        <section className="mb-12">
          <h2 className="text-2xl font-semibold mb-4 text-gray-800 dark:text-gray-200">
            Flex Components
          </h2>
          
          <Layout.Container>
            <div className="mb-6">
              <h3 className="text-lg font-medium mb-2 text-gray-700 dark:text-gray-300">
                Basic Flex Container
              </h3>
              <Layout.Flex className="bg-orange-100 dark:bg-orange-900 border border-orange-300 dark:border-orange-700 rounded p-4 gap-4">
                <Layout.FlexItem className="bg-orange-200 dark:bg-orange-800 rounded p-3 flex-1">
                  <p className="text-sm text-gray-800 dark:text-gray-200">Flex Item 1</p>
                </Layout.FlexItem>
                <Layout.FlexItem className="bg-orange-200 dark:bg-orange-800 rounded p-3 flex-1">
                  <p className="text-sm text-gray-800 dark:text-gray-200">Flex Item 2</p>
                </Layout.FlexItem>
                <Layout.FlexItem className="bg-orange-200 dark:bg-orange-800 rounded p-3 flex-1">
                  <p className="text-sm text-gray-800 dark:text-gray-200">Flex Item 3</p>
                </Layout.FlexItem>
              </Layout.Flex>
            </div>

            <div className="mb-6">
              <h3 className="text-lg font-medium mb-2 text-gray-700 dark:text-gray-300">
                Flex with Custom Classes
              </h3>
              <Layout.Flex className="bg-pink-100 dark:bg-pink-900 border border-pink-300 dark:border-pink-700 rounded p-4 justify-between items-center">
                <Layout.FlexItem className="bg-pink-200 dark:bg-pink-800 rounded p-3">
                  <p className="text-sm text-gray-800 dark:text-gray-200">Left Item</p>
                </Layout.FlexItem>
                <Layout.FlexItem className="bg-pink-200 dark:bg-pink-800 rounded p-3">
                  <p className="text-sm text-gray-800 dark:text-gray-200">Center Item</p>
                </Layout.FlexItem>
                <Layout.FlexItem className="bg-pink-200 dark:bg-pink-800 rounded p-3">
                  <p className="text-sm text-gray-800 dark:text-gray-200">Right Item</p>
                </Layout.FlexItem>
              </Layout.Flex>
            </div>
          </Layout.Container>
        </section>

        {/* Real-world Example */}
        <section className="mb-12">
          <h2 className="text-2xl font-semibold mb-4 text-gray-800 dark:text-gray-200">
            Real-world Example: Form Layout
          </h2>
          
          <Layout.Container>
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
              <h3 className="text-xl font-medium mb-4 text-gray-800 dark:text-gray-200">
                User Registration Form
              </h3>
              
              <Layout.Row>
                <Layout.Col xs={12} md={6}>
                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      First Name
                    </label>
                    <input 
                      type="text" 
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                      placeholder="Enter first name"
                    />
                  </div>
                </Layout.Col>
                <Layout.Col xs={12} md={6}>
                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Last Name
                    </label>
                    <input 
                      type="text" 
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                      placeholder="Enter last name"
                    />
                  </div>
                </Layout.Col>
              </Layout.Row>
              
              <Layout.Row>
                <Layout.Col xs={12}>
                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Email Address
                    </label>
                    <input 
                      type="email" 
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                      placeholder="Enter email address"
                    />
                  </div>
                </Layout.Col>
              </Layout.Row>
              
              <Layout.Row>
                <Layout.Col xs={12} sm={6} md={4}>
                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Country
                    </label>
                    <select className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
                      <option>Select Country</option>
                      <option>United States</option>
                      <option>Canada</option>
                      <option>United Kingdom</option>
                    </select>
                  </div>
                </Layout.Col>
                <Layout.Col xs={12} sm={6} md={4}>
                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      State/Province
                    </label>
                    <input 
                      type="text" 
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                      placeholder="State/Province"
                    />
                  </div>
                </Layout.Col>
                <Layout.Col xs={12} md={4}>
                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      ZIP/Postal Code
                    </label>
                    <input 
                      type="text" 
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                      placeholder="ZIP/Postal Code"
                    />
                  </div>
                </Layout.Col>
              </Layout.Row>
              
              <Layout.Flex className="justify-end gap-4 mt-6">
                <button className="px-4 py-2 text-gray-600 dark:text-gray-400 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700">
                  Cancel
                </button>
                <button className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                  Register
                </button>
              </Layout.Flex>
            </div>
          </Layout.Container>
        </section>

        {/* Usage Instructions */}
        <section className="mb-12">
          <h2 className="text-2xl font-semibold mb-4 text-gray-800 dark:text-gray-200">
            Usage Instructions
          </h2>
          
          <Layout.Container>
            <div className="bg-gray-100 dark:bg-gray-800 rounded-lg p-6">
              <h3 className="text-lg font-medium mb-3 text-gray-800 dark:text-gray-200">
                Import and Use
              </h3>
              <pre className="bg-gray-900 text-green-400 p-4 rounded-md text-sm overflow-x-auto">
{`import Layout from "@repo/ui/components/layout";

// Basic usage
<Layout.Container>
  <Layout.Row>
    <Layout.Col xs={12} md={6}>
      Content 1
    </Layout.Col>
    <Layout.Col xs={12} md={6}>
      Content 2
    </Layout.Col>
  </Layout.Row>
</Layout.Container>

// Flex usage
<Layout.Flex className="justify-between">
  <Layout.FlexItem>Left</Layout.FlexItem>
  <Layout.FlexItem>Right</Layout.FlexItem>
</Layout.Flex>`}
              </pre>
              
              <div className="mt-4">
                <h4 className="font-medium text-gray-800 dark:text-gray-200 mb-2">
                  Available Props:
                </h4>
                <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                  <li><strong>Container:</strong> fluid (boolean), className, style</li>
                  <li><strong>Row:</strong> noGutters (boolean), className, style</li>
                  <li><strong>Col:</strong> xs, sm, md, lg, xl (number | "auto"), offset-* (number), className, style</li>
                  <li><strong>Flex/FlexItem:</strong> className, style, styleOptions (for backward compatibility)</li>
                </ul>
              </div>
            </div>
          </Layout.Container>
        </section>
      </div>
    </div>
  );
};

export default LayoutDemo;
