import React, { useState, useRef, useEffect } from "react";
import { Overlay, Tooltip, Dropdown } from "react-bootstrap";
import Button from "../Button/Button";
import Div from "../Div";
import Text from "../Text/Text";
// Styles
import {
  DropdownItem,
  DropdownLabel,
  DropdownMenu,
  DropdownToggle,
} from "./SidebarMenuItem.styles";

const SidebarMenuItem = (props: any) => {
  const [tooltip, setTooltip] = useState(false);
  const [show, setShow] = useState(false);
  const [position, setPosition] = useState({ x: 0, y: 0 });
  // const history = useHistory();
  const buttonRef = useRef();

  useEffect(() => {
    const cb = () => {
      setShow(false);
    };
    window.addEventListener("click", cb);
    return () => window.removeEventListener("click", cb);
  });

  const getTooltip = () => {
    return (
      <Overlay
        target={() => buttonRef.current || null}
        show={tooltip}
        placement={"right"}
      >
        {(_props) => (
          <Tooltip id={props.title} {..._props}>
            {props.title}
          </Tooltip>
        )}
      </Overlay>
    );
  };

  if (props.items && props.items.length) {
    const getDropdownItemLabel = (item: any) => {
      if (!item.onClick) {
        return item.title;
        // return (
        //   <props.as to={item.route} className="text-white">
        //     {item.title}
        //   </props.as>
        // );
      }
      return item.title;
    };

    const getDropdownMenuTemplate = () => {
      return (
        <DropdownMenu {...position}>
          {props.items.map((item: any) =>
            item.type && item.type === "label" ? (
              <Text.P1
                className="text-muted px-4 py-1"
                key={item.id}
                style={{
                  borderLeftWidth: 5,
                  borderStyle: "solid",
                  borderColor: "transparent",
                }}
              >
                {item.title}
              </Text.P1>
            ) : (
              <props.as to={item.route} className="text-white">
                <DropdownItem
                  onClick={() => (item.onClick ? item.onClick() : "")}
                  key={item.id}
                  as={Div}
                  className="pl-4 cursor-pointer fs-12"
                  isActive={props.pathName.includes(item.route)}
                >
                  {getDropdownItemLabel(item)}
                </DropdownItem>
              </props.as>
            )
          )}
        </DropdownMenu>
      );
    };

    const showMenu = (evt: any) => {
      evt.preventDefault();
      evt.stopPropagation();
      setShow(true);
      const { left, top, width } = evt.currentTarget.getBoundingClientRect();
      setPosition({
        x: left + width,
        y: props.bottom ? top - 80 : top,
      });
    };

    return (
      <Div className="py-2">
        {getTooltip()}
        <Dropdown show={show} drop="right">
          <DropdownToggle
            title={props.buttonTitle}
            ref={buttonRef}
            variant="link"
            className="bg-secondary px-0"
            block
            size="lg"
            onClick={showMenu}
            onMouseEnter={() => {
              setTooltip(true);
            }}
            onMouseLeave={() => {
              setTooltip(false);
            }}
          >
            <DropdownLabel
              title={props.title}
              className={`m-auto ${props.isActive ? "btn-theme" : null}`}
              isActive={props.isActive}
            >
              <div
                className={props.isActive ? "icon-wrap-active" : "icon-wrap"}
              >
                {props.buttonContent}
              </div>
            </DropdownLabel>
          </DropdownToggle>
          {getDropdownMenuTemplate()}
        </Dropdown>
      </Div>
    );
  } else {
    return (
      <Div
        onMouseEnter={() => {
          setTooltip(true);
        }}
        onMouseLeave={() => {
          setTooltip(false);
        }}
      >
        {getTooltip()}
        <Button
          to={props.route}
          as={props.as}
          ref={buttonRef}
          variant="link"
          className="bg-secondary text-white py-2 px-0"
          block
          size="lg"
        >
          <DropdownLabel
            className={`m-auto ${props.isActive ? "btn-theme" : null}`}
            isActive={props.isActive}
          >
            <div className={props.isActive ? "icon-wrap-active" : "icon-wrap"}>
              {props.buttonContent}
            </div>
          </DropdownLabel>
        </Button>
      </Div>
    );
  }
};

export default SidebarMenuItem;
