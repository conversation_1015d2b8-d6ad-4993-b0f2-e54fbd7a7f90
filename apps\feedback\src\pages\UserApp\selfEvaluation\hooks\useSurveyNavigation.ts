import { useState, useCallback } from 'react';
import { useNavigate } from 'react-router';
import { toast } from 'sonner';
import { submitSurvey, type SelfEvaluationSurveyData, type SurveySection } from '@/services/selfEvaluationService';
import { USER_ROUTES } from '@/app.routes';

export const useSurveyNavigation = () => {
  const navigate = useNavigate();
  const [confirmSubmit, setConfirmSubmit] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleNext = useCallback((
    currentSection: SurveySection | null,
    surveyData: SelfEvaluationSurveyData | null,
    setCurrentSection: (section: SurveySection) => void
  ) => {
    if (!currentSection || !surveyData) return;

    const currentIndex = surveyData.sections.findIndex(s => s.id === currentSection.id);
    if (currentIndex < surveyData.sections.length - 1) {
      setCurrentSection(surveyData.sections[currentIndex + 1]);
    } else if (surveyData.meta.canSubmit) {
      setConfirmSubmit(true);
    }
  }, []);

  const handleBack = useCallback((
    currentSection: SurveySection | null,
    surveyData: SelfEvaluationSurveyData | null,
    setCurrentSection: (section: SurveySection) => void
  ) => {
    if (!currentSection || !surveyData) return;

    const currentIndex = surveyData.sections.findIndex(s => s.id === currentSection.id);
    if (currentIndex > 0) {
      setCurrentSection(surveyData.sections[currentIndex - 1]);
    }
  }, []);

  const handleSubmit = useCallback(async (surveyData: SelfEvaluationSurveyData | null) => {
    if (!surveyData) return;

    try {
      setIsSubmitting(true);
      await submitSurvey(surveyData.responseId);
      toast.success('Survey submitted successfully!');
      navigate(USER_ROUTES().dashboard.surveyList);
    } catch (err) {
      console.error('Error submitting survey:', err);
      throw err;
    } finally {
      setIsSubmitting(false);
    }
  }, [navigate]);

  const handleSectionSelect = useCallback((
    section: SurveySection,
    setCurrentSection: (section: SurveySection) => void
  ) => {
    setCurrentSection(section);
    setConfirmSubmit(false);
  }, []);

  return {
    confirmSubmit,
    isSubmitting,
    setConfirmSubmit,
    handleNext,
    handleBack,
    handleSubmit,
    handleSectionSelect
  };
};
