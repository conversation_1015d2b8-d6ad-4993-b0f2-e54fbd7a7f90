# Self Evaluation Survey

This module implements the self-evaluation survey functionality using modern React patterns and reusable components.

## Architecture

The self-evaluation survey follows the same architecture as the engagement and upward surveys:

### Components Structure
```
selfEvaluation/
├── SelfEvaluation.tsx          # Main component
├── components/
│   ├── SurveyHeader.tsx        # Header with title and progress
│   ├── SurveySidebar.tsx       # Sidebar with navigation
│   ├── SurveyContent.tsx       # Main content area
│   └── SubmitConfirmation.tsx  # Submit confirmation dialog
├── hooks/
│   ├── useSurveyData.ts        # Data management hook
│   ├── useSurveyAutoSave.ts    # Auto-save functionality
│   └── useSurveyNavigation.ts  # Navigation logic
└── __tests__/
    └── SelfEvaluation.test.tsx # Test file
```

### Service Layer
- `selfEvaluationService.ts` - API calls and data transformation

## Features

### ✅ Implemented
- **Component-based Architecture**: Modular, reusable components
- **Custom Hooks**: Separation of concerns with dedicated hooks
- **Auto-save**: Debounced auto-save with 1-second delay
- **Real-time Progress**: Live completion percentage updates
- **Section Navigation**: Click to navigate between sections
- **Responsive Design**: Works on desktop and mobile
- **Error Handling**: Proper error states and user feedback
- **Loading States**: Skeleton loaders and loading indicators
- **Theme Support**: Green color scheme for self-evaluation
- **Question Types**: Support for all question types via QuestionRenderer

### 🎨 UI/UX Features
- **Green Theme**: Distinctive green color scheme for self-evaluation
- **Progress Tracking**: Visual progress indicators
- **Section Status**: Shows remaining questions per section
- **Save Status**: Real-time save status in header
- **Responsive Layout**: Mobile-friendly design
- **Consistent Styling**: Matches other survey types

### 🔧 Technical Features
- **TypeScript**: Full type safety
- **React Router 7**: Modern routing
- **Shadcn UI**: Consistent component library
- **Optimistic Updates**: Immediate UI feedback
- **Error Boundaries**: Graceful error handling
- **Accessibility**: ARIA labels and keyboard navigation

## API Integration

The service uses the same API pattern as other surveys:

1. **Get Survey Version**: `/survey/survey-index/{id}`
2. **Create Response**: `/survey/survey-response/` with `SurveyResponseSelf`
3. **Get Structure**: `/survey/survey/{versionId}`
4. **Get Responses**: `/survey/survey-response/{responseId}`
5. **Save Responses**: `/survey/question-response/`
6. **Save Comments**: `/survey/comment-response/`
7. **Submit Survey**: `/survey/survey-response/{responseId}/submit/`

## Usage

```tsx
import SelfEvaluation from './selfEvaluation/SelfEvaluation';

// Route configuration
{
  path: '/survey/:id/self-evaluation',
  component: SelfEvaluation
}
```

## Customization

### Color Scheme
The self-evaluation survey uses a green color scheme to distinguish it from other survey types:
- Header: `bg-green-500`
- Section header: `bg-green-200`
- Progress indicators: `text-green-600`
- Submit button: `bg-green-600`

### Question Types
All question types are supported through the shared `QuestionRenderer` component:
- Rating scales (1-5)
- Multiple choice
- Text input
- Textarea
- Checkboxes

## Testing

Run tests with:
```bash
npm test selfEvaluation
```

## Future Enhancements

1. **FAQ Integration**: Add FAQ support like upward surveys
2. **Analytics**: Add completion analytics
3. **Offline Support**: Cache responses for offline completion
4. **Export**: PDF export functionality
5. **Templates**: Pre-built question templates
6. **Reminders**: Email reminder system

## Related Files

- `@/services/selfEvaluationService.ts` - API service
- `@/components/survey/questions/QuestionRenderer.tsx` - Question component
- `@/app.routes.ts` - Route definitions
- `apps/feedback/src/pages/UserApp/terms/Terms.tsx` - Entry point
