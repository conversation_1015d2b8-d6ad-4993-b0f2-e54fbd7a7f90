name: Launchpad CI/CD

on:
  push:
    branches:
      - beta
  pull_request:
    branches:
      - beta

env:
  AWS_REGION: us-east-1
  NODE_VERSION: 18

permissions:
  contents: read

jobs:
  build_launchpad:
    name: Build Launchpad
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup pnpm
        uses: pnpm/action-setup@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: pnpm

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Build Launchpad
        run: |
          cd apps/launchpad
          pnpm run build-ci-false

      - name: Upload build artifact
        uses: actions/upload-artifact@v4
        with:
          name: launchpad-build
          path: apps/launchpad/dist/**

  deploy_launchpad:
    name: Deploy Launchpad
    runs-on: ubuntu-latest
    needs: build_launchpad

    steps:
      - name: Download build artifact
        uses: actions/download-artifact@v4
        with:
          name: launchpad-build
          path: dist

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}

      - name: Deploy to S3
        run: aws s3 sync dist s3://unmatched-v3-launchpad-frontend-${{ github.ref_name }} --delete
