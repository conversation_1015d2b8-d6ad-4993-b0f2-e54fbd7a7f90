import React, { CSSProperties, ReactNode } from "react";
// import styles from "./Sidebar.module.scss";

export interface Props {
  width: string | number | undefined;
  right?: boolean;
  overlay?: boolean;
  overlayClasses?: boolean;
  className?: string;
  children: ReactNode;
  onOverlayClick?: Function;
  onMouseOver?: Function;
  onMouseLeave?: Function;
  style?: any;
  hasHeader?: boolean;
}

const Sidebar = (props: Props) => {
  const {
    width,
    children,
    right,
    className,
    overlayClasses,
    overlay,
    onOverlayClick,
    onMouseOver,
    onMouseLeave,
    style,
    hasHeader,
  } = props;
  const containerClasses: string = right
    ? "Sidebar-right"
    : "Sidebar-left";
  const containerStyles: CSSProperties = {
    width,
    zIndex: 1060,
    ...style,
  };
  const overlayTemplate = overlay ? (
    <div
      onClick={() => {
        if (onOverlayClick) onOverlayClick();
      }}
      className={`Overlay ${overlayClasses || ""}`}
    ></div>
  ) : null;
  return (
    <>
      <div
        onMouseEnter={(e) => {
          if (onMouseOver) onMouseOver(e);
        }}
        onMouseLeave={(e) => {
          if (onMouseLeave) onMouseLeave(e);
        }}
        className={`${containerClasses} ${className} ${
          hasHeader ? "mt-60" : ""
        }`}
        style={containerStyles}
      >
        <div>{children}</div>
      </div>
      {overlayTemplate}
    </>
  );
};

Sidebar.defaultProps = {
  style: {},
};

export default Sidebar;
