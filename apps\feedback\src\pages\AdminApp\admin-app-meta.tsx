// import appUrls from "unmatched/utils/urls/app-urls";
import ADMIN_URLS from "@/app.routes";
// import icons from "@/assets/icons/icons";

// const {
//   Graph, //Cog, Group,
//   Page,
//   Paste,
//   Email,
// } = icons;

const data = [
  // {
  //   id: 1,
  //   title: "Dashboard",
  //   icon: <Chart />,
  //   route: appUrls.admin.home,
  //   children: [],
  // },
  {
    id: 2,
    title: "Survey",
    icon: (
      <svg
        width="18"
        height="18"
        viewBox="0 0 18 18"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M6.75 12H6.7575M6.75 3.75H5.25C4.85218 3.75 4.47064 3.90804 4.18934 4.18934C3.90804 4.47064 3.75 4.85218 3.75 5.25V14.25C3.75 14.6478 3.90804 15.0294 4.18934 15.3107C4.47064 15.592 4.85218 15.75 5.25 15.75H12.75C13.1478 15.75 13.5294 15.592 13.8107 15.3107C14.092 15.0294 14.25 14.6478 14.25 14.25V5.25C14.25 4.85218 14.092 4.47064 13.8107 4.18934C13.5294 3.90804 13.1478 3.75 12.75 3.75H11.25H6.75ZM6.75 3.75C6.75 4.14782 6.90804 4.52936 7.18934 4.81066C7.47064 5.09196 7.85218 5.25 8.25 5.25H9.75C10.1478 5.25 10.5294 5.09196 10.8107 4.81066C11.092 4.52936 11.25 4.14782 11.25 3.75H6.75ZM6.75 3.75C6.75 3.35218 6.90804 2.97064 7.18934 2.68934C7.47064 2.40804 7.85218 2.25 8.25 2.25H9.75C10.1478 2.25 10.5294 2.40804 10.8107 2.68934C11.092 2.97064 11.25 3.35218 11.25 3.75H6.75ZM9 9H11.25H9ZM9 12H11.25H9ZM6.75 9H6.7575H6.75Z"
          stroke="currentColor"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    ),
    route: ADMIN_URLS().SURVEY.default,
    children: [],
  },
  {
    id: 7,
    title: "Emails",
    icon: (
      <svg
        width="18"
        height="18"
        viewBox="0 0 18 18"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M15 3H3C2.17157 3 1.5 3.67157 1.5 4.5V13.5C1.5 14.3284 2.17157 15 3 15H15C15.8284 15 16.5 14.3284 16.5 13.5V4.5C16.5 3.67157 15.8284 3 15 3Z"
          stroke="currentColor"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M16.5 5.25L9.7725 9.525C9.54095 9.67007 9.27324 9.74701 9 9.74701C8.72676 9.74701 8.45905 9.67007 8.2275 9.525L1.5 5.25"
          stroke="currentColor"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    ),
    route: ADMIN_URLS().EMAIL,
    children: [],
  },
  {
    id: 3,
    title: "Analytics",
    icon: (
      <svg
        width="18"
        height="18"
        viewBox="0 0 18 18"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M6.75 14.25V9.75C6.75 9.35218 6.59196 8.97064 6.31066 8.68934C6.02936 8.40804 5.64782 8.25 5.25 8.25H3.75C3.35218 8.25 2.97064 8.40804 2.68934 8.68934C2.40804 8.97064 2.25 9.35218 2.25 9.75V14.25C2.25 14.6478 2.40804 15.0294 2.68934 15.3107C2.97064 15.592 3.35218 15.75 3.75 15.75H5.25C5.64782 15.75 6.02936 15.592 6.31066 15.3107C6.59196 15.0294 6.75 14.6478 6.75 14.25ZM6.75 14.25V6.75C6.75 6.35218 6.90804 5.97064 7.18934 5.68934C7.47064 5.40804 7.85218 5.25 8.25 5.25H9.75C10.1478 5.25 10.5294 5.40804 10.8107 5.68934C11.092 5.97064 11.25 6.35218 11.25 6.75V14.25M6.75 14.25C6.75 14.6478 6.90804 15.0294 7.18934 15.3107C7.47064 15.592 7.85218 15.75 8.25 15.75H9.75C10.1478 15.75 10.5294 15.592 10.8107 15.3107C11.092 15.0294 11.25 14.6478 11.25 14.25M11.25 14.25V3.75C11.25 3.35218 11.408 2.97064 11.6893 2.68934C11.9706 2.40804 12.3522 2.25 12.75 2.25H14.25C14.6478 2.25 15.0294 2.40804 15.3107 2.68934C15.592 2.97064 15.75 3.35218 15.75 3.75V14.25C15.75 14.6478 15.592 15.0294 15.3107 15.3107C15.0294 15.592 14.6478 15.75 14.25 15.75H12.75C12.3522 15.75 11.9706 15.592 11.6893 15.3107C11.408 15.0294 11.25 14.6478 11.25 14.25Z"
          stroke="currentColor"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    ),
    route: ADMIN_URLS().ANALYTICS.default,
    children: [
      // {
      //   id: 1,
      //   checkActive: `${appUrls.admin.analytics.default}/stats`,
      //   route: appUrls.admin.analytics.statistics.default,
      //   title: "Statistics",
      // },
      {
        id: 1,
        checkActive: false,
        route: ADMIN_URLS().ANALYTICS.statistics.default,
        title: "360, Upward & Self",
        type: "label",
      },
      {
        id: 2,
        checkActive: `${ADMIN_URLS().ANALYTICS.default}/people`,
        route: ADMIN_URLS().ANALYTICS.people.list,
        title: "People Analytics",
      },
      {
        id: 3,
        checkActive: `${ADMIN_URLS().ANALYTICS.default}/aggregate`,
        route: ADMIN_URLS().ANALYTICS.getAggregateUrl(),
        title: "Aggregate Analytics",
      },
      {
        id: 6,
        checkActive: `${ADMIN_URLS().ANALYTICS.default}/ranking`,
        route: ADMIN_URLS().ANALYTICS.geRankingListUrl(),
        title: "Ranking List",
      },
      {
        id: 4,
        checkActive: false,
        route: "",
        title: "Engagement",
        type: "label",
      },
      {
        id: 5,
        checkActive: `${ADMIN_URLS().ANALYTICS.default}/engagement`,
        route: ADMIN_URLS().ANALYTICS.engagement.default,
        title: "Engagement Analytics",
      },
      {
        id: 6,
        checkActive: false,
        route: "",
        title: "Exit Analytics",
        type: "label",
      },
      {
        id: 7,
        checkActive: `${ADMIN_URLS().ANALYTICS.default}/exit`,
        route: ADMIN_URLS().ANALYTICS.exit.default,
        title: "Exit Analytics",
      },
    ],
  },
  {
    id: 4,
    title: "Reports",
    icon: (
      <svg
        width="18"
        height="18"
        viewBox="0 0 18 18"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M6.75 12.75V11.25V12.75ZM9 12.75V9.75V12.75ZM11.25 12.75V8.25V12.75ZM12.75 15.75H5.25C4.85218 15.75 4.47064 15.592 4.18934 15.3107C3.90804 15.0294 3.75 14.6478 3.75 14.25V3.75C3.75 3.35218 3.90804 2.97064 4.18934 2.68934C4.47064 2.40804 4.85218 2.25 5.25 2.25H9.4395C9.6384 2.25004 9.82913 2.32909 9.96975 2.46975L14.0303 6.53025C14.1709 6.67087 14.25 6.8616 14.25 7.0605V14.25C14.25 14.6478 14.092 15.0294 13.8107 15.3107C13.5294 15.592 13.1478 15.75 12.75 15.75Z"
          stroke="currentColor"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    ),
    route: ADMIN_URLS().REPORTS.default,
    children: [],
  },
  // {
  //   id: 5,
  //   title: "Settings",
  //   icon: <Cog />,
  //   route: appUrls.admin.settings.getURL(),
  //   children: [],
  // },
  // {
  //   id: 6,
  //   title: "Data Load",
  //   icon: <Group />,
  //   route: appUrls.admin.dataLoad.default,
  //   children: [],
  // },
];

export default data;
