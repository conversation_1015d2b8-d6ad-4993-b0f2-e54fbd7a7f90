import React, { useState, useEffect } from "react";
import util from "unmatched/utils";
import { getMetaLabelsFact } from "./filters-api";

export function useLocalFilter(
  id?: any,
  exclude?: boolean,
  filterFor?: string,
) {
  const [localFilters, setLocalFilters] = useState<any>([]);
  const [localSelected, setLocalSelected] = React.useState<any>({});

  useEffect(() => {
    fetchFilters();
  }, [filterFor]);

  const fetchFilters = (cb?: Function) => {
    getMetaLabelsFact({
      survey_id: id,
      exclude_disabled: exclude,
      ...(filterFor && { filter_for: filterFor }),
    }).then((response: any) => {
      setLocalFilters(response);
      if (cb) cb(response);
    });
  };

  const getLocalFilters = (cb?: Function) => {
    if (!util.lib.isEmpty(localFilters)) {
      if (cb) cb(localFilters);
    } else {
      fetchFilters(cb);
    }
  };

  const onLocalSelect = (item: any) => {
    setLocalSelected(item);
  };

  const getParams: any = (_selected: any) => {
    let params = {};
    Object.keys(_selected).forEach((key: string) => {
      const values = util.lib.get(_selected, key);
      if (!values.length) return;
      params = { ...params, [key]: util.getCommaSeperatedFromArray(values) };
    });
    return params;
  };

  return {
    getParams,
    filters: localFilters,
    selected: localSelected,
    onSelect: onLocalSelect,
    getFilters: getLocalFilters,
  };
}
