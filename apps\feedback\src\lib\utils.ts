import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

/**
 * Get the API base URL based on the current environment
 * @returns The API base URL with /api/v2 appended
 */
export function getApiBaseUrl(): string {
  // In production, use the current domain
  if (import.meta.env.MODE === "production") {
    return `${window.location.origin}/api/v2`;
  }

  // Check for environment variable first
  if (import.meta.env.VITE_API_BASE_URL) {
    return import.meta.env.VITE_API_BASE_URL;
  }

  // Fallback to beta environment for development (feedback app uses beta)
  return "https://alpha.unmatched.app/api/v2";
}
