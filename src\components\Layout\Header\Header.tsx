import React, { ReactNode, useEffect } from "react";
import { Navbar, Nav } from "react-bootstrap";
import Icon from "../../Icon";

interface HeaderProps {
  title: ReactNode;
  information?: ReactNode;
  metaItem?: ReactNode;
  toggleTemplate?: ReactNode;
  className?: string;
  style?: any;
  marginLeft?: string;
}

const Header = (props: HeaderProps) => {
  const { title, metaItem, style, information, toggleTemplate, marginLeft } =
    props;
  const padding = "25px 30px 10px 30px";
  const styles = {
    marginLeft,
    padding,
    ...style,
    borderBottom: '1px solid #dee2e6',
  };
  const [show, setShow] = React.useState(false);

  const [positionY, setPositionY] = React.useState(0);
  const getScrollPosition = () => {
    setPositionY(window.scrollY);
  };
  useEffect(() => {
    window.addEventListener("scroll", getScrollPosition);
    return () => {
      window.removeEventListener("scroll", getScrollPosition);
    };
  }, []);
  return (
    <Navbar
      variant="light"
      className={`${props.className || ""} bg-white ${
        positionY > 1 ? "shadow-sm" : ""
      }`}
      style={styles}
      fixed="top"
      expand="md"
    >
      {toggleTemplate}
      <Navbar.Brand>{title}</Navbar.Brand>
      <Navbar.Toggle
        onClick={() => setShow(!show)}
        className="ml-auto"
        aria-controls="responsive-navbar-nav"
      >
        <Icon icon={show ? "fal fa-minus-square" : "fal fa-plus-square"} />
      </Navbar.Toggle>
      <Navbar.Collapse id="responsive-navbar-nav">
        {information && <Nav>{information}</Nav>}
        {metaItem && <Nav className="ml-auto">{metaItem}</Nav>}
      </Navbar.Collapse>
    </Navbar>
  );
};

export default Header;
