import React from "react";
import FormControl from "../Form/FormControl/FormControl";
import Icon from "../Icon";
import Layout from "../Layout/Layout";
import Table from "../Table/Table";
import Div from "../Div";

const ComboMultiFilter = (props: any) => {
  const { cohart, applied, onCohartUpdate, onAppliedUpdate, hideIcon } = props;

  return (
    <Layout.Row>
      {!hideIcon && (
        <Div className="pr-0 pt-1 col" style={{ maxWidth: 30 }}>
          <Icon icon="far fa-filter" />
        </Div>
      )}
      <Div className="pr-0 col-5">
        <FormControl.Select variant="white" value={cohart.selected}>
          {cohart.options.map((item: any) => {
            return (
              <FormControl.SelectItem
                onSelect={() => {
                  onCohartUpdate(item.key);
                }}
                key={item.key}
              >
                {item.title}
              </FormControl.SelectItem>
            );
          })}
        </FormControl.Select>
      </Div>
      <Div className="pr-0 col-4">
        <Table.FilterMultiSelect
          onOptionsSelect={onAppliedUpdate}
          selected={applied.selected}
          options={applied.options}
        />
      </Div>
    </Layout.Row>
  );
};

export default ComboMultiFilter;
