import React from 'react';
import { SurveyCard, SurveyCardSkeleton } from './SurveyCard';

interface SurveysSectionProps {
  title: string;
  surveys: any[];
  statsMap: any;
  loading: boolean;
  isOngoing: (survey: any) => boolean;
  onViewDetails: (surveyId: string) => void;
  emptyMessage?: string;
}

export const SurveysSection: React.FC<SurveysSectionProps> = ({
  title,
  surveys,
  statsMap,
  loading,
  isOngoing,
  onViewDetails,
  emptyMessage = 'No surveys available.'
}) => {
  if (surveys.length === 0 && !loading) {
    return (
      <div className="text-center py-8 text-gray-500 dark:text-gray-400">
        {emptyMessage}
      </div>
    );
  }

  return (
    <div className="mb-8">
      <div className="flex items-center gap-1 text-sm font-medium text-gray-700 dark:text-gray-300 mb-4">
        <span className="px-3 py-1 bg-blue-100 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 rounded-full">
          {title}
        </span>
      </div>
      
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {loading ? (
          Array(3).fill(0).map((_, index) => (
            <SurveyCardSkeleton key={index} />
          ))
        ) : (
          surveys.map((survey) => (
            <SurveyCard
              key={survey.id}
              survey={survey}
              statsMap={statsMap}
              isOngoing={isOngoing(survey)}
              onViewDetails={onViewDetails}
            />
          ))
        )}
      </div>
    </div>
  );
};
