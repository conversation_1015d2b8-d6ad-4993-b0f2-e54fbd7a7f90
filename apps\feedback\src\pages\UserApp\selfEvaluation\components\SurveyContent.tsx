import React from 'react';
import { Button } from '@repo/ui/components/button';
import QuestionRenderer from '@/components/survey/questions/QuestionRenderer';
import { type SelfEvaluationSurveyData, type SurveySection } from '@/services/selfEvaluationService';

interface SurveyContentProps {
  isLoading: boolean;
  isSaving: boolean;
  surveyData: SelfEvaluationSurveyData | null;
  currentSection: SurveySection | null;
  lastSaved: Date;
  onResponseUpdate: (questionId: string, value: any, responseId?: string, shouldDelete?: boolean) => void;
  onCommentUpdate: (questionId: string, comment: string, commentId?: string) => void;
  onNext: () => void;
  onBack: () => void;
}

const SurveyContent: React.FC<SurveyContentProps> = ({
  isLoading,
  isSaving: _isSaving,
  surveyData,
  currentSection,
  lastSaved: _lastSaved,
  onResponseUpdate,
  onCommentUpdate,
  onNext,
  onBack
}) => {
  if (isLoading) {
    return (
      <div className="flex-1 bg-white dark:bg-gray-800 flex flex-col overflow-hidden">
        <div className="p-6 space-y-6">
          <div className="h-8 bg-gray-200 dark:bg-gray-600 rounded animate-pulse w-3/4"></div>
          <div className="h-32 bg-gray-200 dark:bg-gray-600 rounded animate-pulse w-full"></div>
          <div className="h-32 bg-gray-200 dark:bg-gray-600 rounded animate-pulse w-full"></div>
        </div>
      </div>
    );
  }

  if (!surveyData) {
    return (
      <div className="flex-1 bg-white dark:bg-gray-800 flex items-center justify-center">
        <p className="text-gray-600 dark:text-gray-400">No survey data available</p>
      </div>
    );
  }

  if (!currentSection) {
    return (
      <div className="flex-1 bg-white dark:bg-gray-800 flex items-center justify-center">
        <p className="text-gray-600 dark:text-gray-400">No sections available</p>
      </div>
    );
  }

  return (
    <div className="flex-1 bg-white dark:bg-gray-800 flex flex-col overflow-hidden">
      {/* Section Header - Light green bar for self-evaluation */}
      <div className="bg-green-200 dark:bg-green-800 px-6 py-4 border-b dark:border-gray-700 flex-shrink-0">
        <h2 className="text-lg font-medium text-gray-800 dark:text-white">{currentSection.title}</h2>
      </div>

      {/* Scrollable Questions Content */}
      <div className="flex-1 overflow-y-auto p-6">
        <div className="space-y-8">
          {currentSection.questions.length === 0 ? (
            <p className="text-gray-600 dark:text-gray-400">No questions in this section</p>
          ) : (
            currentSection.questions.map((question, index) => (
              <QuestionRenderer
                key={question.id}
                question={question}
                questionNumber={index + 1}
                onResponseUpdate={onResponseUpdate}
                onCommentUpdate={onCommentUpdate}
              />
            ))
          )}
        </div>
      </div>

      {/* Fixed Footer */}
      <div className="bg-white dark:bg-gray-800 border-t dark:border-gray-700 flex-shrink-0">
        {/* Navigation Footer */}
        <div className="px-6 py-2">
          <div className="flex justify-between items-center">
            <div className="flex items-center space-x-4">
              {/* Back button for section navigation */}
              {surveyData?.sections && currentSection && surveyData.sections.findIndex(s => s.id === currentSection.id) > 0 && (
                <Button
                  onClick={onBack}
                  variant="outline"
                  className="px-6"
                >
                  Back
                </Button>
              )}
              <div className="text-sm text-gray-600 dark:text-gray-400">
                {currentSection && currentSection.questions.length > 0 && (
                  <span>
                    {currentSection.questions.filter(q =>
                      q.response?.value === undefined || q.response?.value === null || q.response?.value === ''
                    ).length} Questions left in this section
                  </span>
                )}
              </div>
            </div>
            <Button
              onClick={onNext}
              className="bg-green-600 hover:bg-green-700 dark:bg-green-600 dark:hover:bg-green-700 px-8"
            >
              {surveyData?.sections && currentSection &&
               surveyData.sections.findIndex(s => s.id === currentSection.id) === surveyData.sections.length - 1
                ? 'Submit my response'
                : 'Next'
              }
            </Button>
          </div>
        </div>

        {/* Privacy/Terms Footer */}
        <div className="border-t dark:border-gray-700 px-6 py-3 bg-gray-50 dark:bg-gray-900">
          <div className="flex justify-center items-center space-x-4 text-xs text-gray-500 dark:text-gray-400">
            <a
              href="/terms"
              target="_blank"
              rel="noopener noreferrer"
              className="hover:text-gray-700 dark:hover:text-gray-300 transition-colors"
            >
              Terms
            </a>
            <span>•</span>
            <a
              href="/privacy"
              target="_blank"
              rel="noopener noreferrer"
              className="hover:text-gray-700 dark:hover:text-gray-300 transition-colors"
            >
              Privacy Policy
            </a>
            <span>•</span>
            <a
              href="/confidentiality"
              target="_blank"
              rel="noopener noreferrer"
              className="hover:text-gray-700 dark:hover:text-gray-300 transition-colors"
            >
              Confidentiality
            </a>
            <span>•</span>
            <a
              href="/contact"
              target="_blank"
              rel="noopener noreferrer"
              className="hover:text-gray-700 dark:hover:text-gray-300 transition-colors"
            >
              Contact Us
            </a>
            <span>•</span>
            <span className="text-gray-400 dark:text-gray-500">
              Unmatched © {new Date().getFullYear()}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SurveyContent;
