import React, { ReactElement } from "react";
import styled from "styled-components";

interface Props {
  width?: string;
  size?: string;
  className?: string;
}

export default function Placeholder({
  size,
  width,
  className,
}: Props): ReactElement {
  return (
    <PlaceHolderEl size={size} className={className}>
      <span className={`${width ? width : "col12"} placeholder`}></span>
    </PlaceHolderEl>
  );
}
const PlaceHolderEl = styled("div")<{ size?: string }>`
  .placeholder {
    animation: placeholder-glow 2s ease-in-out infinite;
  }
  .placeholder {
    display: inline-block;
    min-height: ${(props: any) => (props.size ? props.size : 1)}em;
    vertical-align: middle;
    cursor: wait;
    background-color: #a9a9a9;
    border-radius: 20px;
    opacity: 0.5;
  }
  @keyframes placeholder-glow {
    50% {
      opacity: 0.2;
    }
  }
`;
