import React from "react";
import _ from "lodash";
import Div from "../../../Div";
import Icon from "../../../Icon";
import {
  FormControlProps,
  getFormControlAttributes,
  INPUT_TYPES,
  formControlDefaultProps,
} from "../form-control";
// import classes from "../../Forms.module.scss";
import { useDebounce } from "react-use";

interface SearchProps extends FormControlProps {
  onSearch?: Function;
  delay?: number;
  style?: any;
}

const Search = React.forwardRef((props: SearchProps, ref: any) => {
  const _computed = getFormControlAttributes(props, ref);
  const [value, setValue] = React.useState("");
  const [touched, setTouched] = React.useState(false);

  useDebounce(
    () => {
      if (props.onSearch && touched) props.onSearch(value);
    },
    props.delay,
    [value]
  );

  const attributes = _.omit(
    {
      ..._computed,
      className: `${_computed.className} pl-4 py-2 h-100`,
      type: INPUT_TYPES.TEXT,
      onChange: (e: any) => {
        setValue(e.target.value);
        if (props.onChange) props.onChange(e);
      },
      onBlur: (e: any) => {
        setValue(e.target.value);
        if (props.onBlur) props.onBlur(e);
      },
      onFocus: () => {
        if (!touched) {
          setTouched(true);
        }
      },
    },
    ["onSearch", "delay"]
  );

  return (
    <Div className={"search-container"}>
      <Div className={"search-icon"}>
        <Icon icon="fal fa-search fs-12" />
      </Div>
      <input style={props.style || {}} {...attributes} />
    </Div>
  );
});

Search.defaultProps = { ...formControlDefaultProps, delay: 1000 };

export default Search;
