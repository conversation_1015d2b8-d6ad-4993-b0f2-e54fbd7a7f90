import React, { useState } from 'react';
import { Button } from '@repo/ui/components/button';
import { Badge } from '@repo/ui/components/badge';
import { Popover, PopoverContent, PopoverTrigger } from '@repo/ui/components/popover';
import { Checkbox } from '@repo/ui/components/checkbox';
import { X, Plus, ChevronDown } from 'lucide-react';
import { cn } from '@/lib/utils';

interface FilterMetadata {
  labels: Array<{
    field: string;
    display_name: string;
    type: string;
  }>;
  values: Record<string, string[]>;
}

interface FilterState {
  [key: string]: string[];
}

interface MultiCriteriaFilterProps {
  metadata: FilterMetadata;
  filters: FilterState;
  onFiltersChange: (filters: FilterState) => void;
  className?: string;
}

const MultiCriteriaFilter: React.FC<MultiCriteriaFilterProps> = ({
  metadata,
  filters,
  onFiltersChange,
  className
}) => {
  const [, setSelectedField] = useState<string>('');
  const [isAddFilterOpen, setIsAddFilterOpen] = useState(false);

  // Get available fields that haven't been added yet
  const availableFields = metadata.labels.filter(
    label => !Object.keys(filters).includes(label.field)
  );

  // Handle adding a new filter
  const handleAddFilter = (field: string) => {
    if (field && !filters[field]) {
      onFiltersChange({
        ...filters,
        [field]: []
      });
      setSelectedField('');
      setIsAddFilterOpen(false);
    }
  };

  // Handle removing a filter
  const handleRemoveFilter = (field: string) => {
    const newFilters = { ...filters };
    delete newFilters[field];
    onFiltersChange(newFilters);
  };

  // Handle updating filter values
  const handleFilterValueChange = (field: string, value: string, checked: boolean) => {
    const currentValues = filters[field] || [];
    let newValues: string[];

    if (checked) {
      newValues = [...currentValues, value];
    } else {
      newValues = currentValues.filter(v => v !== value);
    }

    onFiltersChange({
      ...filters,
      [field]: newValues
    });
  };

  // Get display name for a field
  const getFieldDisplayName = (field: string) => {
    const label = metadata.labels.find(l => l.field === field);
    return label?.display_name || field;
  };

  // Clear all filters
  const handleClearAll = () => {
    onFiltersChange({});
  };

  return (
    <div className={cn("space-y-4", className)}>
      {/* Filter Tags and Add Button */}
      <div className="flex flex-wrap items-center gap-2">
        {/* Existing Filter Tags */}
        {Object.entries(filters).map(([field, values]) => (
          <FilterTag
            key={field}
            field={field}
            displayName={getFieldDisplayName(field)}
            values={values}
            availableValues={metadata.values[field] || []}
            onValueChange={(value, checked) => handleFilterValueChange(field, value, checked)}
            onRemove={() => handleRemoveFilter(field)}
          />
        ))}

        {/* Add Filter Button */}
        {availableFields.length > 0 && (
          <Popover open={isAddFilterOpen} onOpenChange={setIsAddFilterOpen}>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                size="sm"
                className="h-8 border-dashed"
              >
                <Plus className="h-4 w-4 mr-1" />
                Add a filter
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-48 p-2" align="start">
              <div className="space-y-1">
                {availableFields.map((label) => (
                  <Button
                    key={label.field}
                    variant="ghost"
                    size="sm"
                    className="w-full justify-start h-8 px-2"
                    onClick={() => handleAddFilter(label.field)}
                  >
                    {label.display_name}
                  </Button>
                ))}
              </div>
            </PopoverContent>
          </Popover>
        )}

        {/* Clear All Button */}
        {Object.keys(filters).length > 0 && (
          <Button
            variant="ghost"
            size="sm"
            onClick={handleClearAll}
            className="h-8 px-2 text-muted-foreground hover:text-foreground"
          >
            <X className="h-4 w-4 mr-1" />
            Clear all
          </Button>
        )}
      </div>
    </div>
  );
};

// Individual Filter Tag Component
interface FilterTagProps {
  field: string;
  displayName: string;
  values: string[];
  availableValues: string[];
  onValueChange: (value: string, checked: boolean) => void;
  onRemove: () => void;
}

const FilterTag: React.FC<FilterTagProps> = ({
  field,
  displayName,
  values,
  availableValues,
  onValueChange,
  onRemove
}) => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="flex items-center">
      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <Badge
            variant="secondary"
            className="cursor-pointer bg-[#c8dbff] hover:bg-secondary/10 pr-1 pl-3 py-1 h-8"
          >
            <span className="font-bold">{displayName}</span>
            {values.length > 0 && (
              <span className="ml-1 text-foreground font-normal">
                {values.join(', ')}
              </span>
            )}
            <ChevronDown className="h-3 w-3 ml-1" />
          </Badge>
        </PopoverTrigger>
        <PopoverContent className="w-64 p-3" align="start">
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <h4 className="font-medium text-sm">{displayName}</h4>
              <Button
                variant="ghost"
                size="sm"
                onClick={onRemove}
                className="h-6 w-6 p-0 hover:bg-destructive hover:text-destructive-foreground"
              >
                <X className="h-3 w-3" />
              </Button>
            </div>
            <div className="space-y-2 max-h-48 overflow-y-auto">
              {availableValues.map((value) => (
                <div key={value} className="flex items-center space-x-2">
                  <Checkbox
                    id={`${field}-${value}`}
                    checked={values.includes(value)}
                    onCheckedChange={(checked) => 
                      onValueChange(value, checked as boolean)
                    }
                  />
                  <label
                    htmlFor={`${field}-${value}`}
                    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer"
                  >
                    {value}
                  </label>
                </div>
              ))}
            </div>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  );
};

export default MultiCriteriaFilter;
