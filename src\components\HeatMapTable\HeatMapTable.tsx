import _ from "lodash";
import React from "react";
import styled from "styled-components";
import util from "../../utils";
import Div from "../Div";
// import Layout from "../Layout/Layout";
import Text from "../Text/Text";
// import PropTypes from 'prop-types'

const StyledData = styled.td`
  padding-top: 10px !important;
  padding-bottom: 10px !important;
  text-align: center !important;
  border: 1px solid #f2f2f2 !important;
  /* color: #fff; */
  background-color: ${(props: any) => props.backgroundColor || ""};
`;

const colors = ["#98BDD7", "#699DC8", "#487DB4", "#2D5CA1", "#15337C"];

const getBackgroundColor = (index: any, _colors: any) => {
  return _colors[index === -1 ? 0 : index];
};

const getValuesMeta = (values: any) => {
  const uniqValues = _.uniqBy(values, (item: any) => item);
  const sortedValues = _.sortBy(uniqValues, (item: any) => item, "asc");
  const min = sortedValues[0];
  const max = sortedValues[sortedValues.length - 1];
  return {
    uniqValues,
    sortedValues,
    min,
    max,
  };
};

const getFieldData = (value: number, values: any) => {
  const parts = colors.length;
  let _colors = colors;
  if (values && values.length) {
    const { sortedValues, min, max } = getValuesMeta(values);
    let index = -1;
    if (sortedValues.length < parts) {
      _colors = _.drop(_colors, parts - sortedValues.length);
      index = sortedValues.indexOf(value);
    } else {
      const chunks = util.splitArrayToChunks(sortedValues, parts);
      // console.log(chunks);
      chunks.forEach((item: any, _index: number) => {
        if (item.includes(value)) {
          index = _index;
        }
      });
    }
    return {
      colorIndex: index,
      colors: _colors,
      min,
      max,
      values: sortedValues,
    };
  }
  return {
    colorIndex: util.math.roundOff(value),
    colors: _colors,
  };
};

const Data = (props: any) => {
  const fieldData: any = getFieldData(
    props.value,
    props.values?.map((item: any) => Number(item))
  );

  const attributes = {
    ..._.omit(props, ["children", "value", "values"]),
    backgroundColor: getBackgroundColor(fieldData.colorIndex, fieldData.colors),
  };
  return (
    <StyledData {...attributes}>
      <Text.P1>
        {!isNaN(props.children) ? (
          <span className="text-white">{props.children}</span>
        ) : (
          props.children
        )}
      </Text.P1>
    </StyledData>
  );
};

const HeatMapTable = (props: any) => {
  // values
  const { columns, rows, rowItem } = props;
  // const { min, max } = getValuesMeta(values);

  // const getScaleNumber = (index: number) => {
  //   let val = null;
  //   if (!index) {
  //     val = min;
  //   } else if (index === colors.length - 1) {
  //     val = max;
  //   }
  //   return val;
  // };

  return (
    <div className="table-responsive">
      <table className="table table-bordered">
        <thead>
          <tr className="py-5 text-center">
            {columns.map((item: any) => {
              return (
                <th
                  className="bg-light"
                  // key={item.id}
                  key={`${
                    Math.floor(
                      Math.random() * Math.floor(Math.random() * Date.now())
                    ) + item.id
                  }`}
                  data-key={`${
                    Math.floor(
                      Math.random() * Math.floor(Math.random() * Date.now())
                    ) + item.id
                  }`}
                  style={{
                    width: `${100 / columns.length}%`,
                    verticalAlign: "middle",
                  }}
                >
                  <Text.P1>{item.label}</Text.P1>
                </th>
              );
            })}
          </tr>
        </thead>
        <tbody>
          {rows && rows.length ? (
            rows.map((item: any) => {
              return <tr key={item.id}>{rowItem(item)}</tr>;
            })
          ) : (
            <Div className="p-3">No Data Found</Div>
          )}
        </tbody>
      </table>
      {/* {rows && !!rows.length && (
        <Layout.Flex className="justify-content-end my-3">
          <Div className="align-self-end pr-3">Scale</Div>
          {colors.map((item: any, index: number) => {
            return (
              <Div key={item} className={!index ? "text-left" : "text-right"}>
                <Div
                  style={{
                    backgroundColor: item,
                    width: "40px",
                    height: "20px",
                  }}
                ></Div>
              </Div>
            );
          })}
        </Layout.Flex>
      )} */}
    </div>
  );
};

HeatMapTable.Data = Data;

// HeatMapTable.propTypes = {

// }

export default HeatMapTable;
