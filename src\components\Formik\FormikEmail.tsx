import React from "react";
import { isFieldInvalid, getFieldErrorMessage } from "../../utils/formik";
import FormControl from "../Form/FormControl/FormControl";
import FormGroup from "../Form/FormGroup/FormGroup";

interface Props {
  label?: string;
  key: string;
  placeholder?: string;
  formik: any;
}

export default function FormikEmailInput(props: Props) {
  const { label, key, placeholder, formik } = props;

  return (
    <FormGroup>
      <FormGroup.Label>{label}</FormGroup.Label>
      <FormControl.Email
        name={key}
        isInvalid={isFieldInvalid(formik, key)}
        value={formik.values[key]}
        onBlur={formik.handleBlur}
        onChange={formik.handleChange}
        placeholder={placeholder}
      ></FormControl.Email>
      <FormGroup.InValidFeedback
        text={getFieldErrorMessage(formik, key)}
      ></FormGroup.InValidFeedback>
    </FormGroup>
  );
}
