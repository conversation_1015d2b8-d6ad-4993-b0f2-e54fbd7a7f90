import { useState, useEffect, useCallback } from 'react';
import { surveyService } from '../services/surveyService';
import { SurveyStatsMap } from '../types/stats';

export const useSurveys = (page: number, pageSize: number) => {
  const [surveys, setSurveys] = useState<any[]>([]);
  const [statsMap, setStatsMap] = useState<SurveyStatsMap>({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [totalCount, setTotalCount] = useState(0);

  const fetchSurveys = useCallback(async () => {
    try {
      setLoading(true);
      const response = await surveyService.getSurveys(page, pageSize);
      
      const surveys = Array.isArray(response) ? response : (response?.surveys || response?.data || []);
      const sortedSurveys = [...surveys].sort(
        (a, b) => {
          const dateA = a.deadline ? new Date(a.deadline).valueOf() : 0;
          const dateB = b.deadline ? new Date(b.deadline).valueOf() : 0;
          return dateA - dateB;
        }
      );
      
      setSurveys(sortedSurveys);
      setTotalCount(response.totalCount || response.count || sortedSurveys.length);
      setError(null);
      
      if (sortedSurveys.length > 0) {
        const stats = await surveyService.getSurveysWithStats(sortedSurveys);
        setStatsMap(stats);
      }
    } catch (err) {
      console.error('Error fetching surveys:', err);
      setError('Failed to load surveys');
    } finally {
      setLoading(false);
    }
  }, [page, pageSize]);

  useEffect(() => {
    fetchSurveys();
  }, [fetchSurveys]);

  return {
    surveys,
    statsMap,
    loading,
    error,
    totalCount,
    refetch: fetchSurveys
  };
};

export const useSurveyStatus = () => {
  const isOngoing = useCallback((survey: any) => {
    try {
      if (!survey) return false;
      
      const now = new Date().getTime();
      const deadline = survey.resourcetype === "SurveyIndexExit" 
        ? (survey.participant_deadline || survey.deadline)
        : survey.deadline;
      
      if (!deadline || !survey.start) return false;
      
      const start = new Date(survey.start).getTime();
      const endTime = new Date(deadline).getTime();
      
      return now < endTime && now > start;
    } catch (error) {
      console.error('Error in isOngoing check:', error);
      return false;
    }
  }, []);

  const getProgressPercentage = useCallback((statsMap: SurveyStatsMap, surveyId: string) => {
    const stats = statsMap[surveyId];
    return stats ? stats.percentage_completed : 0;
  }, []);

  const getStatusCounts = useCallback((statsMap: SurveyStatsMap, surveyId: string) => {
    const stats = statsMap[surveyId] || {
      questions_left: 0,
      questions_completed: 0,
      percentage_completed: 0,
      total_questions: 0
    };
    
    return [
      { label: 'Questions Left', count: stats.questions_left, color: 'bg-gray-200' },
      { label: 'Completed', count: stats.questions_completed, color: 'bg-green-200' },
      { label: 'Total', count: stats.total_questions, color: 'bg-blue-200' }
    ];
  }, []);

  return {
    isOngoing,
    getProgressPercentage,
    getStatusCounts
  };
};
