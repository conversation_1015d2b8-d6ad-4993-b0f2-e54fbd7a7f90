/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ReactNode } from "react";
// import { EnhancedHeader } from "unmatched/components";
import { useAdminContext } from "../../Provider";
import ToggleSidebar from "./ToggleSidebar";

interface HeaderProps {
  title: ReactNode;
  information?: ReactNode;
  metaItem?: ReactNode;
  className?: string;
  style?: any;
  breadcrumbs?: any;
  noShadow?: boolean;
}

const CustomHeader = (props: HeaderProps) => {
  const meta = useAdminContext();
  const marginLeft = meta.margin ? "60px" : "0px";

  const getToggleTemplate = () => (
    <ToggleSidebar
      margin={meta.margin}
      sidebar={meta.sidebar}
      setSidebar={meta.setSidebar}
    />
  );

  // const customProps = {
  //   ...props,
  //   marginLeft,
  //   style: {
  //     ...props.style,
  //     zIndex: 1049,
  //   },
  //   // className: `${props.className} mt-60`,
  //   className: `${props.className}`,
  //   toggleTemplate: getToggleTemplate(),
  // };

  // return <EnhancedHeader {...customProps} />;
  return (
    <>
      {getToggleTemplate()} {marginLeft} {props.title}
    </>
  );
};

export default CustomHeader;
