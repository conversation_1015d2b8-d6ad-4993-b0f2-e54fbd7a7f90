import styled from "styled-components";
import { Dropdown } from "react-bootstrap";
import Div from "../Div";

export const DropdownItem = styled(Dropdown.Item)`
  color: #fff !important;
  font-weight: 500 !important;
  font-size: 0.875rem !important;
  padding-top: 10px;
  padding-bottom: 10px;
  border-left: 5px solid #1b2945 !important;
  &:hover {
    color: #fff !important;
    background-color: #121b2e !important;
    // border-left: 5px solid #518cff !important;
  }
  &:focus {
    color: #fff !important;
    background-color: #121b2e !important;
    // border-left: 5px solid #518cff !important;
  }
`;

export const DropdownMenu = styled(Dropdown.Menu)`
  background-color: #1b2945 !important;
  position: fixed !important;
  left: -3px !important;
  border-radius: 0px;
  min-width: 200px;
  transform: translate(
    ${(props: any) => `${props.x}px, ${props.y}px`}
  ) !important;
`;

export const DropdownToggle = styled(Dropdown.Toggle)`
  border-radius: 0px !important;
  box-sizing: border-box;
  border-radius: 5px;
  &:focus {
    outline: none !important;
  }
  &:active {
    outline: none !important;
  }
  &::after {
    display: none !important;
  }
`;

export const DropdownLabel = styled(Div)`
  ${(props) =>
    props.isActive
      ? `
    color: #fff;
    font-size: 14px;
    // padding: 5px 5px 8px 5px;
    border-radius: 4px;
    width: 34px;
    height: 34px;
  `
      : ""}
`;
