import React, { ReactNode } from "react";
import Text from "../../Text/Text";
// import classes from "../Forms.module.scss";

export interface FormGroupProps {
  children?: ReactNode;
  className?: string;
}

const Description = (props: {
  text?: string;
  children?: ReactNode;
  className?: string;
}) => {
  const { children, className, text } = props;
  const labelClass = `text-muted ${className}`;
  const Description = children ? children : text;
  return <Text.P2 className={labelClass}>{Description}</Text.P2>;
};

const InValidFeedback = (props: {
  text?: string;
  children?: ReactNode;
  className?: string;
}) => {
  const { children, className, text } = props;
  const labelClass = `text-danger ${className}`;
  const FeedbackNode = children ? children : text;
  return <Text.P2 className={labelClass}>{FeedbackNode}</Text.P2>;
};

const ValidFeedback = (props: {
  text?: string;
  children?: ReactNode;
  className?: string;
}) => {
  const { children, className, text } = props;
  const labelClass = `text-success ${className}`;
  const FeedbackNode = children ? children : text;
  return <Text.P2 className={labelClass}>{FeedbackNode}</Text.P2>;
};

export const Label = (props: {
  label?: string;
  children?: ReactNode;
  isRequired?: boolean;
  className?: string;
}) => {
  const { children, className, label, isRequired } = props;
  const LabelNode = children ? (
    children
  ) : (
    <>
      {label} {isRequired && <span className={"text-danger"}>*</span>}
    </>
  );
  const labelClass = `form-text ${className} pb-1`;
  return <Text.InputLabel className={labelClass}>{LabelNode}</Text.InputLabel>;
};

const FormGroup = (props: FormGroupProps) => {
  const { children, className } = props;
  const formGroupClass = `form-group ${className}`;
  return <div className={formGroupClass}>{children}</div>;
};

const formGroupDefaultProps = {
  className: "",
};

Label.defaultProps = { ...formGroupDefaultProps };
ValidFeedback.defaultProps = { ...formGroupDefaultProps };
InValidFeedback.defaultProps = { ...formGroupDefaultProps };
Description.defaultProps = { ...formGroupDefaultProps };
FormGroup.defaultProps = { ...formGroupDefaultProps };

FormGroup.Label = Label;
FormGroup.ValidFeedback = ValidFeedback;
FormGroup.InValidFeedback = InValidFeedback;
FormGroup.Description = Description;

export default FormGroup;
