# Shared Survey Components

This directory contains reusable components that can be shared across different survey types (engagement, upward, etc.).

## Suggested Shared Components

### 1. BaseSurveyLayout
A common layout component that provides the basic structure for all survey types.

### 2. SurveyProgressIndicator
A reusable progress indicator component.

### 3. SurveyNavigationButtons
Common navigation buttons (Next, Back, Submit) with consistent styling.

### 4. SurveyErrorBoundary
Error boundary component for handling survey-related errors.

### 5. SurveyLoadingStates
Standardized loading states and skeleton loaders.

## Benefits

- **Consistency**: Ensures all survey types have the same look and feel
- **Maintainability**: Changes to shared components affect all survey types
- **Reduced Code Duplication**: Common functionality is centralized
- **Easier Testing**: Shared components can be tested once and reused

## Implementation Plan

1. Extract common components from engagement and upward surveys
2. Create generic interfaces that work for all survey types
3. Update existing survey implementations to use shared components
4. Add comprehensive tests for shared components
