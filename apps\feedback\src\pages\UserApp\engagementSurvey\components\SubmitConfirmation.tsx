import React from 'react';
import { But<PERSON> } from '@repo/ui/components/button';
import { Card, CardContent, CardHeader, CardTitle } from '@repo/ui/components/card';

interface SubmitConfirmationProps {
  isSubmitting: boolean;
  onBack: () => void;
  onSubmit: () => void;
}

const SubmitConfirmation: React.FC<SubmitConfirmationProps> = ({
  isSubmitting,
  onBack,
  onSubmit
}) => {
  return (
    <div className="min-h-screen flex items-center justify-center p-6">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle>Submit Survey</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <p>Are you ready to submit your responses?</p>
          <div className="flex gap-2">
            <Button 
              variant="outline" 
              onClick={onBack}
              className="flex-1"
              disabled={isSubmitting}
            >
              Back
            </Button>
            <Button 
              onClick={onSubmit}
              disabled={isSubmitting}
              className="flex-1"
            >
              {isSubmitting ? 'Submitting...' : 'Submit'}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default SubmitConfirmation;
