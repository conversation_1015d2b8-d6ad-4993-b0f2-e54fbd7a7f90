import React from "react";
import styled from "styled-components";
import { Text } from "../../components";

type Props = {
  sentiment: string;
  className?: string;
};

export default function Sentiment({ sentiment, className }: Props) {
  const colors = () => {
    const colorBlocks: any = {
      POSITIVE: "#36B37E",
      NEGATIVE: "#C02E0A",
      NEUTRAL: "#0071F6",
      MIXED: "#313131",
    };
    return colorBlocks[sentiment];
  };
  return (
    <TextBlock className={className} color={colors}>
      {sentiment.length > 0 &&
        sentiment
          .split(" ")
          .map((w) => w[0].toUpperCase() + w.substring(1).toLowerCase())
          .join(" ")}
    </TextBlock>
  );
}
const TextBlock = styled(Text.P1)<any>`
  color: ${(props: any) => props.color};
`;
