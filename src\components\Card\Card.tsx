import React from "react";
import Text from "../Text/Text";
import Div from "../Div";

interface CardProps {
  children: React.ReactNode;
  className?: string;
  noShadow?: boolean;
  onClick?: Function;
  style?: any;
}

const Header = (props: CardProps) => {
  const { children, className, style } = props;
  const containerClass = `${className} bg-light`;
  return (
    <Div styles={style} className={containerClass}>
      {children}
    </Div>
  );
};

const Title = (props: CardProps) => {
  const { children } = props;
  return <Text.H3 className="text-primary pb-2">{children}</Text.H3>;
};

const Body = (props: CardProps) => {
  const { children, className } = props;
  return (
    <div
      className={`p-2 pb-3 p-xl-4 p-lg-4 p-md-3 p-sm-3 p-xs-3  ${
        className || ""
      }`}
    >
      {children}
    </div>
  );
};

const Footer = (props: CardProps) => {
  const { children, className } = props;
  const defaultClass = "text-right py-3";
  return <div className={`${defaultClass} ${className || ""}`}>{children}</div>;
};

const Card = (props: CardProps) => {
  const { className, children, onClick, noShadow, style } = props;
  const shadowClass = noShadow ? "shadow-none" : "";
  const defaultClass = `card ${shadowClass}`;
  const cardClass = className ? `${defaultClass} ${className}` : defaultClass;
  return (
    <div
      onClick={() => {
        if (onClick) onClick();
      }}
      className={cardClass}
      style={style}
    >
      {children}
    </div>
  );
};

Card.defaultPros = {
  className: "",
};

Title.defaultPros = {
  className: "",
};

Body.defaultPros = {
  className: "",
};

Footer.defaultPros = {
  className: "",
};

Card.Title = Title;
Card.Body = Body;
Card.Footer = Footer;
Card.Header = Header;

export default Card;
