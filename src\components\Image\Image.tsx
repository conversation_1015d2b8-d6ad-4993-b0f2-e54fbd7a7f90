import React from "react";
import _ from "lodash";
import LazyLoad from "react-lazyload";
// import styles from "./Image.module.scss";

interface ImageProps extends React.ImgHTMLAttributes<HTMLImageElement> {
  lazy?: boolean;
  contain?: boolean;
  cover?: boolean;
  position?: "left" | "center" | "right";
  className?: string;
}

const CUSTOM_PROPS = ["lazy", "alt", "contain", "position"];

const getImageClassName = (props: ImageProps) => {
  const { className, contain, cover, position } = props;
  const containClass = contain ? "img-contain" : "";
  const coverClass = cover ? "img-cover" : "";
  const positionClass = `contain-${position}`;
  return `${containClass} ${positionClass} ${coverClass} ${className}`;
};

const Image = (props: ImageProps) => {
  const attributes = {
    ..._.omit(props, CUSTOM_PROPS),
    className: getImageClassName(props),
  };
  const Image = <img alt={props.alt} {...attributes}></img>;
  if (props.lazy) {
    return <LazyLoad>{Image}</LazyLoad>;
  }
  return Image;
};

Image.defaultProps = {
  contain: true,
  position: "left",
};

export default Image;
