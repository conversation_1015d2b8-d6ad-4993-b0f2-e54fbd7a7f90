import React, { ReactNode, forwardRef } from "react";
import Text from "../../Text/Text";
// import classes from "../Forms.module.scss";
// import sliderClasses from "../Switch.module.scss";
import {
  formControlDefaultProps,
  FormControlProps,
  getCheckAttributes,
  getFormControlAttributes,
  INPUT_TYPES,
} from "./form-control";
import Select, { SelectItem } from "./Select/Select";
import Search from "./Search/Search";
import SearchWithSuggestion from "./SearchWithSuggestion/SearchWithSuggestion";
// import data from "assets/icons/icons";
// import { Field } from "formik";

const TextInput = forwardRef((props: FormControlProps, ref: any) => {
  const attributes = {
    ...getFormControlAttributes(props, ref),
    type: INPUT_TYPES.TEXT,
  };
  if (attributes.formik && attributes.formik.field) {
    return <attributes.formik.field {...attributes} />;
  }
  return <input {...attributes} />;
});

const DateInput = forwardRef((props: FormControlProps, ref: any) => {
  const attributes = {
    ...getFormControlAttributes(props, ref),
    type: INPUT_TYPES.TEXT,
  };

  const dateImage =
    "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";

  return (
    <input
      {...attributes}
      style={{ background: `url(${dateImage}) 95% 50% no-repeat` }}
    />
  );
});

const Textarea = forwardRef((props: FormControlProps, ref: any) => {
  const attributes = {
    ...getFormControlAttributes(props, ref),
  };
   if (attributes.formik && attributes.formik.field) {
    return <attributes.formik.field {...attributes} />;
  }
  return <textarea {...attributes} />;
});

const NumberInput = forwardRef((props: FormControlProps, ref: any) => {
  const attributes = {
    ...getFormControlAttributes(props, ref),
    type: INPUT_TYPES.NUMBER,
  };
  return <input {...attributes} />;
});

const Email = forwardRef((props: FormControlProps, ref: any) => {
  const attributes = {
    ...getFormControlAttributes(props, ref),
    type: INPUT_TYPES.EMAIL,
    onInvalid: (e: any) => {
      e.preventDefault();
    },
  };
  return <input {...attributes} />;
});

const Password = forwardRef((props: FormControlProps, ref: any) => {
  const attributes = {
    ...getFormControlAttributes(props, ref),
    type: INPUT_TYPES.PASSWORD,
  };
  return <input {...attributes} />;
});

// Checkboxes, radio buttons

const RadioInput = forwardRef((props: FormControlProps, ref: any) => {
  const attributes = {
    ...getCheckAttributes(props, ref),
    type: INPUT_TYPES.RADIO,
  };
  return <input {...attributes} />;
});

const CheckboxInput = forwardRef((props: FormControlProps, ref: any) => {
  const attributes = {
    ...getCheckAttributes(props, ref),
    type: INPUT_TYPES.CHECKBOX,
  };
  return <input {...attributes} />;
});

const CheckLabel = (props: any) => {
  const { className } = props;
  return (
    <Text.InputLabel
      className={`form-check-label ${className} pt-1`}
    >
      {props.children}
    </Text.InputLabel>
  );
};

const RadioLabel = CheckLabel;

const Checkbox = (props: { className?: string; children?: ReactNode }) => {
  const { className, children } = props;
  return (
    <div className={`form-check d-flex ${className}`}>
      {children}
    </div>
  );
};

const Radio = (props: { className?: string; children?: ReactNode }) => {
  const { className, children } = props;
  return (
    <div className={`form-check d-flex ${className}`}>
      {children}
    </div>
  );
};

const Switch = forwardRef((props: FormControlProps, ref: any) => {
  const attributes = {
    ...getCheckAttributes(props, ref),
    className: `${props.className}`,
    type: INPUT_TYPES.CHECKBOX,
  };
  return (
    <>
      <label className={"switch-unmatched"}>
        <input {...attributes} />
        <span
          className={`slider-unmatched round-unmatched`}
        ></span>
      </label>
    </>
  );
});

// const Select = forwardRef((props: FormControlProps, ref: any) => {
//   const attributes = {
//     ...getFormControlAttributes(props, ref),
//     className: `${classes["custom-select"]} ${
//       classes[`custom-select-${props.size}`]
//     } ${props.className}`,
//     type: INPUT_TYPES.SELECT,
//   };
//   return <select {...attributes}>{props.children}</select>;
// });

CheckLabel.defaultProps = { ...formControlDefaultProps };
CheckboxInput.defaultProps = { ...formControlDefaultProps };
RadioLabel.defaultProps = { ...formControlDefaultProps };
RadioInput.defaultProps = { ...formControlDefaultProps };
TextInput.defaultProps = { ...formControlDefaultProps };
NumberInput.defaultProps = { ...formControlDefaultProps };
Email.defaultProps = { ...formControlDefaultProps };
Password.defaultProps = { ...formControlDefaultProps };
Textarea.defaultProps = { ...formControlDefaultProps };
Radio.defaultProps = { ...formControlDefaultProps };
Checkbox.defaultProps = { ...formControlDefaultProps };
Switch.defaultProps = { ...formControlDefaultProps };
// Select.defaultProps = { ...formControlDefaultProps, size: "sm" };

Checkbox.Label = CheckLabel;
Checkbox.Input = CheckboxInput;

Radio.Label = RadioLabel;
Radio.Input = RadioInput;

const FormControl: any = {
  Text: TextInput,
  Number: NumberInput,
  Date: DateInput,
  Email,
  Password,
  Textarea,
  Radio,
  Checkbox,
  Switch,
  Select,
  Search,
  SearchWithSuggestion,
  SelectItem,
};

export default FormControl;
