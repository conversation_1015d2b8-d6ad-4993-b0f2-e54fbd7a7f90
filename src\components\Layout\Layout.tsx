import React from "react";
import { Container, Row, Col } from "react-bootstrap";
import getUtilClassName from "../../utils/styles";
import Header from "./Header/Header";
// import { forOwn, pick } from "lodash";
import Sidebar from "./Sidebar/Sidebar";

// const Container = (props: {
//   fluid?: boolean;
//   children?: React.ReactNode;
//   className?: string;
// }) => {
//   const containerClass = props.fluid ? "container-fluid" : "container";
//   return (
//     <div className={`${containerClass} ${props.className || ""}`}>
//       {props.children}
//     </div>
//   );
// };

// const Row = (props: { children?: React.ReactNode; className?: string }) => {
//   const containerClass = "row";
//   return (
//     <div className={`${containerClass} ${props.className || ""}`}>
//       {props.children}
//     </div>
//   );
// };

// const breakpoints: Array<string> = [
//   "xl",
//   "lg",
//   "md",
//   "sm",
//   "xs",
//   "offset-xl",
//   "offset-lg",
//   "offset-md",
//   "offset-sm",
//   "offset-xs",
// ];

// const Col = (props: {
//   children?: React.ReactNode;
//   xl?: number;
//   lg?: number;
//   md?: number;
//   sm?: number;
//   xs?: number;
//   "offset-xl"?: number;
//   "offset-lg"?: number;
//   "offset-md"?: number;
//   "offset-sm"?: number;
//   "offset-xs"?: number;
//   className?: string;
// }) => {
//   let defaultClass = "col";
//   forOwn(pick(props, breakpoints), (value, key) => {
//     let breakpointClass = "";
//     let offsetClass = "";
//     if (value) {
//       if (key.includes("offset")) {
//         offsetClass = `${key}-${value}`;
//       } else {
//         breakpointClass = `col-${key}-${value}`;
//       }
//     }
//     defaultClass = `${defaultClass} ${breakpointClass} ${offsetClass}`;
//   });
//   return (
//     <div className={`${defaultClass} ${props.className || ""}`}>
//       {props.children}
//     </div>
//   );
// };

const FlexItem = (props: {
  children?: React.ReactNode;
  className?: string;
  styleOptions?: any;
}) => {
  const { children, className, styleOptions } = props;
  const styles = styleOptions ? getUtilClassName(styleOptions) : "";
  return (
    <div
      className={`text-left text-wrap text-break ${styles} ${className || ""}`}
    >
      {children}
    </div>
  );
};

const Flex = (props: {
  children?: React.ReactNode;
  className?: string;
  styleOptions?: any;
}) => {
  const { children, className, styleOptions } = props;
  const styles = styleOptions ? getUtilClassName(styleOptions) : "";
  return (
    <div className={`d-flex  ${styles} ${className || ""}`}>{children}</div>
  );
};

const Layout:any = {
  Container,
  Row,
  Col,
  Flex,
  FlexItem,
  Sidebar,
  Header,
};

export default Layout;
