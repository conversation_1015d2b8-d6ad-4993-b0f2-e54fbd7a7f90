// import Auth from "./Auth/Auth";
import DesignSystem from "./DesignSystem";
import LayoutDemo from "./LayoutDemo";
import AdminApp from "./AdminApp/AdminApp";
import UserApp from "./UserApp/UserApp";
import appUrls from "unmatched/utils/urls/app-urls";
import Logout from "./Logout";
import FAQs from "./Faq/Container";
import ContactUs from "./ContactUs/Container";
import Privacy from "./Privacy/Container";
import Terms from "./Terms/Container";
import Confidentiality from "./Confidentiality/Container";
import Landing from "./Landing";

const routes = [
  // {
  //   name: "Authentication",
  //   path: appUrls.auth.default,
  //   isExact: false,
  //   component: Auth,
  // },
  {
    name: "Feedback App",
    path: "/",
    isExact: true,
    component: Landing,
  },
  {
    name: "Feedback App",
    path: appUrls.admin.default,
    isExact: false,
    component: AdminApp,
  },
  {
    name: "User App",
    path: appUrls.user.default,
    isExact: false,
    component: UserApp,
  },
  {
    name: "Design System",
    path: appUrls.design,
    isExact: false,
    component: DesignSystem,
  },
  {
    name: "Layout Demo",
    path: "/layout-demo",
    isExact: false,
    component: LayoutDemo,
  },
  {
    name: "Logout",
    path: appUrls.logout,
    isExact: false,
    component: Logout,
  },
  {
    name: "FAQ's",
    path: appUrls.faq,
    isExact: false,
    component: FAQs,
  },
  {
    name: "Privacy Policy",
    path: appUrls.privacy,
    isExact: false,
    component: Privacy,
  },
  {
    name: "Terms",
    path: appUrls.terms,
    isExact: false,
    component: Terms,
  },
  {
    name: "Confidentiality",
    path: appUrls.confidentiality,
    isExact: false,
    component: Confidentiality,
  },
  {
    name: "Contact Us",
    path: appUrls.contactUs,
    isExact: false,
    component: ContactUs,
  },
];

export default routes;
